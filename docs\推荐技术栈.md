# DPPaaS 数字产品护照平台 - MVP技术栈设计方案

## 需求分析与技术选型

### 核心业务需求（基于概括文档）

**业务场景分析**:
1. **生产环节**: 经营者注册(EORI) → 产品注册 → DPP创建 → 二维码生成 → VC管理
2. **清关环节**: 海关扫码 → UID查询 → DPP验证 → VC验证
3. **托管服务**: 数字签名、数据完整性、Registry集成

**技术要求**:
- JSON-LD格式的DPP数据（40个核心字段）
- ECDSA_P256_SHA256数字签名
- W3C可验证凭证(VC)标准
- 二维码/NFC载体生成
- 机器可读、数字签名、不可篡改

**盈利模式**: 对每个产品的二维码收费 + 回收抵扣券服务

### MVP技术栈设计原则

1. **快速开发**: 优先选择成熟稳定的技术栈
2. **成本控制**: 选择性价比高的技术方案
3. **扩展性**: 为后续功能扩展预留空间
4. **合规性**: 满足欧盟ESPR法规要求
5. **维护性**: 技术栈统一，降低维护成本

## 推荐MVP技术栈

### 整体架构选择: 单体应用 + 微服务混合

**理由**: MVP阶段优先开发速度，核心功能用单体应用快速实现，非核心功能可独立微服务

### 1. 前端技术栈

**方案A: Next.js (推荐)**
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Next.js** | 14.x | React全栈框架 | 成熟稳定，API路由内置，开发效率高 |
| **TypeScript** | 5.x | 类型系统 | 类型安全，减少bug |
| **Tailwind CSS** | 3.x | CSS框架 | 快速UI开发，组件丰富 |
| **Shadcn/ui** | 最新 | 组件库 | 现成组件，设计统一 |
| **React Hook Form** | 7.x | 表单管理 | 处理复杂表单（40个字段） |

**方案B: Nuxt.js (备选)**
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Nuxt.js** | 3.x | Vue 3框架 | SSR支持，SEO友好 |
| **Vue 3** | 3.x | 前端框架 | 学习成本低，开发快速 |
| **Tailwind CSS** | 3.x | CSS框架 | 与Next.js方案保持一致 |

**推荐选择**: Next.js，理由是生态更成熟，组件库更丰富，适合快速MVP开发

### 2. 后端技术栈

**方案A: Node.js (推荐)**
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Node.js** | 20 LTS | 运行时 | 与前端技术栈统一，开发效率高 |
| **Express.js** | 4.x | Web框架 | 成熟稳定，中间件丰富 |
| **TypeScript** | 5.x | 类型系统 | 前后端类型统一 |
| **Prisma** | 5.x | ORM | 类型安全，迁移管理便捷 |
| **node-forge** | 1.x | 加密库 | ECDSA数字签名支持 |

**方案B: Go微服务 (备选)**
| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Go** | 1.21+ | 后端语言 | 高性能，并发处理好 |
| **Gin** | 1.9+ | Web框架 | 轻量级，性能优秀 |
| **GORM** | 1.25+ | ORM | Go生态最成熟的ORM |

**推荐选择**: Node.js，理由是MVP阶段优先开发速度，技术栈统一

### 3. 数据库选择

**方案A: PostgreSQL (推荐)**
| 技术 | 配置 | 用途 | 选择理由 |
|------|------|------|----------|
| **PostgreSQL** | 15+ | 主数据库 | JSONB支持，适合DPP数据存储 |
| **Redis** | 7+ | 缓存 | 会话存储，查询缓存 |

**方案B: MySQL (备选)**
| 技术 | 配置 | 用途 | 选择理由 |
|------|------|------|----------|
| **MySQL** | 8.0+ | 主数据库 | JSON字段支持，生态成熟 |

**推荐选择**: PostgreSQL，理由是JSONB字段更适合存储DPP的JSON-LD数据

### 4. 认证方案

**方案A: NextAuth.js (推荐)**
| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **NextAuth.js** | 认证框架 | Next.js原生支持，配置简单 |
| **JWT** | 令牌管理 | 无状态认证 |
| **bcryptjs** | 密码加密 | 安全的密码哈希 |

**方案B: Keycloak (备选)**
| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **Keycloak** | 身份管理 | 企业级功能，多租户支持 |

**推荐选择**: NextAuth.js，理由是MVP阶段功能够用，配置简单

### 5. 部署方案

**方案A: 云服务器 (推荐)**
| 服务 | 配置 | 用途 |
|------|------|------|
| **应用服务器** | 2核4G | 运行Next.js应用 |
| **数据库** | PostgreSQL托管服务 | 数据存储 |
| **Redis** | 1G内存 | 缓存服务 |
| **对象存储** | 标准存储 | 文件存储 |

**方案B: AWS (备选)**
| 服务 | 配置 | 用途 |
|------|------|------|
| **EC2** | t3.small | 应用服务器 |
| **RDS** | PostgreSQL | 数据库 |
| **ElastiCache** | Redis | 缓存 |
| **S3** | 标准存储 | 文件存储 |

**推荐选择**: 云服务器，理由是成本更低，配置更灵活

## 最终推荐技术栈

基于业务需求分析和技术选型对比，推荐以下MVP技术栈：

### 核心技术栈组合

```
前端: Next.js 14 + TypeScript + Tailwind CSS
后端: Node.js + Express.js + TypeScript
数据库: PostgreSQL + Redis
认证: NextAuth.js + JWT
部署: Docker + 云服务器
```

### 详细技术栈配置

#### 1. 前端技术栈
```json
{
  "framework": "Next.js 14.x",
  "language": "TypeScript 5.x",
  "styling": "Tailwind CSS 3.x",
  "components": "Shadcn/ui",
  "forms": "React Hook Form 7.x",
  "state": "Zustand 4.x",
  "http": "Axios"
}
```

#### 2. 后端技术栈
```json
{
  "runtime": "Node.js 20 LTS",
  "framework": "Express.js 4.x",
  "language": "TypeScript 5.x",
  "orm": "Prisma 5.x",
  "auth": "NextAuth.js 4.x",
  "crypto": "node-forge 1.x",
  "validation": "Zod 3.x",
  "qrcode": "qrcode 1.x"
}
```

#### 3. 数据存储
```json
{
  "database": "PostgreSQL 15+",
  "cache": "Redis 7+",
  "storage": "本地文件系统/对象存储",
  "orm": "Prisma"
}
```

#### 4. 开发工具
```json
{
  "containerization": "Docker",
  "version_control": "Git",
  "ci_cd": "GitHub Actions",
  "testing": "Jest + Cypress",
  "linting": "ESLint + Prettier"
}
```

## 整体架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    DPPaaS MVP 架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Next.js)                                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   经营者门户     │  │   海关查询页面   │  │   管理后台   │  │
│  │   (Dashboard)   │  │   (Public API)  │  │   (Admin)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  API层 (Express.js)                                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           RESTful API + 认证中间件                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Node.js Services)                              │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐         │
│  │经营者管理服务│ │产品管理服务  │ │DPP生成服务   │         │
│  └──────────────┘ └──────────────┘ └──────────────┘         │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐         │
│  │VC管理服务    │ │载体生成服务  │ │查询验证服务  │         │
│  └──────────────┘ └──────────────┘ └──────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                     │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐         │
│  │PostgreSQL    │ │Redis缓存     │ │文件存储      │         │
│  │主数据库      │ │会话/临时数据 │ │图片/证书     │         │
│  └──────────────┘ └──────────────┘ └──────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 核心数据模型设计

#### 1. 经营者表
```sql
CREATE TABLE economic_operators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  eori_number VARCHAR(17) UNIQUE NOT NULL,  -- EORI号码
  company_name VARCHAR(255) NOT NULL,
  contact_email VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(50),
  business_address JSONB NOT NULL,
  certification_status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. 产品表
```sql
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  uid VARCHAR(255) UNIQUE NOT NULL,  -- 产品唯一标识符
  economic_operator_id UUID REFERENCES economic_operators(id),
  product_name VARCHAR(255) NOT NULL,
  product_category VARCHAR(100) NOT NULL,
  manufacturer VARCHAR(255) NOT NULL,
  model_number VARCHAR(100),
  batch_number VARCHAR(100),
  production_date DATE,
  core_attributes JSONB NOT NULL,  -- 40个核心字段
  carbon_footprint DECIMAL(10,3),
  recyclability_rate DECIMAL(5,2),
  energy_efficiency_class VARCHAR(10),
  status VARCHAR(20) DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. 数字产品护照表
```sql
CREATE TABLE digital_passports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),
  service_provider_id VARCHAR(100) NOT NULL,
  dpp_endpoint VARCHAR(500) NOT NULL,
  passport_data JSONB NOT NULL,  -- JSON-LD格式DPP数据
  signature TEXT NOT NULL,  -- ECDSA_P256_SHA256数字签名
  signature_algorithm VARCHAR(50) DEFAULT 'ECDSA_P256_SHA256',
  public_key TEXT NOT NULL,
  version INTEGER DEFAULT 1,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. 可验证凭证表
```sql
CREATE TABLE verifiable_credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),
  credential_id VARCHAR(255) UNIQUE NOT NULL,
  issuer_did VARCHAR(255) NOT NULL,  -- 签发机构DID
  credential_type VARCHAR(100) NOT NULL,
  credential_data JSONB NOT NULL,  -- VC数据
  proof JSONB NOT NULL,  -- 数字签名证明
  valid_from TIMESTAMP NOT NULL,
  valid_until TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 5. 数据载体表
```sql
CREATE TABLE data_carriers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),
  carrier_type VARCHAR(20) NOT NULL,  -- 'qr_code', 'nfc', 'barcode'
  carrier_data TEXT NOT NULL,  -- 载体数据
  encoded_url VARCHAR(500),  -- 访问URL
  gtin VARCHAR(14),  -- 全球贸易项目代码
  serial_number VARCHAR(50),
  generated_at TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 核心业务逻辑实现

### 1. DPP生成服务 (Node.js/TypeScript)

```typescript
// DPP核心字段接口 (40个必需字段)
interface DPPCoreFields {
  // 基础标识信息
  uid: string;
  productName: string;
  manufacturer: string;
  modelNumber: string;
  batchNumber: string;
  productionDate: Date;

  // 材料信息
  materials: MaterialInfo[];
  recycleRate: number;

  // 环保信息
  carbonFootprint: number;
  energyEfficiency: string;

  // 合规信息
  certifications: Certification[];
  complianceStatus: string;

  // 供应链信息
  supplyChainInfo: SupplyChainNode[];

  // 其他核心字段...
}

// JSON-LD DPP文档结构
interface DPPDocument {
  "@context": string[];
  "id": string;
  "type": string[];
  "credentialSubject": DPPCoreFields;
  "issuer": string;
  "issuanceDate": Date;
  "proof": DigitalSignature;
}

// ECDSA_P256_SHA256数字签名
interface DigitalSignature {
  type: "ECDSA_P256_SHA256";
  created: Date;
  verificationMethod: string;
  proofPurpose: "assertionMethod";
  proofValue: string;
}
```

### 2. 数字签名实现 (Node.js + node-forge)

```typescript
import forge from 'node-forge';
import crypto from 'crypto';

class ECDSASignatureService {
  private privateKey: forge.pki.PrivateKey;
  private publicKey: forge.pki.PublicKey;

  // 生成DPP数字签名
  async signDPP(dppData: DPPCoreFields): Promise<DigitalSignature> {
    // 1. 规范化JSON-LD数据
    const canonicalData = this.canonicalizeJSONLD(dppData);

    // 2. 生成SHA256哈希
    const hash = crypto.createHash('sha256').update(canonicalData).digest();

    // 3. ECDSA签名
    const signature = this.privateKey.sign(hash);
    const proofValue = forge.util.encode64(signature);

    return {
      type: "ECDSA_P256_SHA256",
      created: new Date(),
      verificationMethod: this.getPublicKeyDID(),
      proofPurpose: "assertionMethod",
      proofValue: proofValue
    };
  }

  // 验证DPP数字签名
  async verifyDPP(dppDoc: DPPDocument): Promise<boolean> {
    // 实现签名验证逻辑
    // ...
  }
}
```

### 3. API接口设计

#### 经营者管理API
```typescript
// POST /api/operators - 注册经营者
app.post('/api/operators', async (req, res) => {
  const { eoriNumber, companyName, contactEmail, businessAddress } = req.body;

  const operator = await prisma.economicOperator.create({
    data: {
      eoriNumber,
      companyName,
      contactEmail,
      businessAddress
    }
  });

  res.json(operator);
});

// GET /api/operators/:eori - 查询经营者
app.get('/api/operators/:eori', async (req, res) => {
  const operator = await prisma.economicOperator.findUnique({
    where: { eoriNumber: req.params.eori }
  });

  res.json(operator);
});
```

#### 产品管理API
```typescript
// POST /api/products - 注册产品
app.post('/api/products', async (req, res) => {
  const { productName, manufacturer, coreAttributes } = req.body;

  // 生成唯一UID
  const uid = generateProductUID();

  const product = await prisma.product.create({
    data: {
      uid,
      productName,
      manufacturer,
      coreAttributes
    }
  });

  res.json(product);
});

// GET /api/products/:uid - 查询产品
app.get('/api/products/:uid', async (req, res) => {
  const product = await prisma.product.findUnique({
    where: { uid: req.params.uid }
  });

  res.json(product);
});
```

#### DPP管理API
```typescript
// POST /api/dpp - 创建DPP
app.post('/api/dpp', async (req, res) => {
  const { productId } = req.body;

  // 获取产品信息
  const product = await prisma.product.findUnique({
    where: { id: productId }
  });

  // 生成DPP数据
  const dppData = generateDPPData(product);

  // 数字签名
  const signature = await signatureService.signDPP(dppData);

  // 保存DPP
  const dpp = await prisma.digitalPassport.create({
    data: {
      productId,
      passportData: dppData,
      signature: signature.proofValue,
      publicKey: signature.verificationMethod
    }
  });

  res.json(dpp);
});

// GET /api/dpp/:uid - 海关查询DPP
app.get('/api/dpp/:uid', async (req, res) => {
  const dpp = await prisma.digitalPassport.findFirst({
    where: {
      product: {
        uid: req.params.uid
      }
    },
    include: {
      product: true
    }
  });

  res.json(dpp);
});
```

### 4. 载体生成API

```typescript
// POST /api/carriers/qr - 生成二维码
app.post('/api/carriers/qr', async (req, res) => {
  const { productId, size = '200x200' } = req.body;

  // 获取产品信息
  const product = await prisma.product.findUnique({
    where: { id: productId }
  });

  // 生成访问URL
  const accessUrl = `https://dppaas.com/passport/${product.uid}`;

  // 生成二维码
  const qrCode = await QRCode.toDataURL(accessUrl);

  // 保存载体信息
  const carrier = await prisma.dataCarrier.create({
    data: {
      productId,
      carrierType: 'qr_code',
      carrierData: qrCode,
      encodedUrl: accessUrl
    }
  });

  res.json(carrier);
});
```

### 5. VC管理API

```typescript
// POST /api/vc - 上传VC
app.post('/api/vc', async (req, res) => {
  const { productId, credentialData, issuerDid } = req.body;

  // 验证VC格式
  const isValid = await validateVCFormat(credentialData);
  if (!isValid) {
    return res.status(400).json({ error: 'Invalid VC format' });
  }

  // 保存VC
  const vc = await prisma.verifiableCredential.create({
    data: {
      productId,
      credentialId: credentialData.id,
      issuerDid,
      credentialType: credentialData.type[1],
      credentialData,
      proof: credentialData.proof,
      validFrom: new Date(credentialData.validFrom),
      validUntil: credentialData.validUntil ? new Date(credentialData.validUntil) : null
    }
  });

  res.json(vc);
});

// GET /api/vc/:productId - 获取产品相关VC
app.get('/api/vc/:productId', async (req, res) => {
  const vcs = await prisma.verifiableCredential.findMany({
    where: {
      productId: req.params.productId,
      status: 'active'
    }
  });

  res.json(vcs);
});
```

## 部署方案

### Docker容器化配置

```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production

# 生成Prisma客户端
RUN npx prisma generate

# 复制应用代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

### Docker Compose开发环境

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=******************************************/dppaas
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=your-secret-key
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: dppaas
      POSTGRES_USER: dppaas
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## 开发计划

### 第一阶段（3周）- 基础平台搭建

**Week 1: 项目初始化**
- Next.js项目搭建，配置TypeScript和Tailwind CSS
- 数据库设计，Prisma schema定义
- 基础认证系统（NextAuth.js）

**Week 2: 核心数据模型**
- 经营者管理功能
- 产品注册功能
- 基础CRUD操作

**Week 3: DPP生成核心**
- JSON-LD格式生成
- 数字签名实现
- DPP数据存储

### 第二阶段（2周）- 核心功能完善

**Week 4: 载体生成和VC管理**
- 二维码生成功能
- VC上传和验证
- 海关查询接口

**Week 5: 集成和优化**
- DPP Registry集成
- 性能优化
- 安全加固

### 第三阶段（1周）- 部署和测试

**Week 6: 生产部署**
- 容器化部署
- 生产环境配置
- 端到端测试

## 总结

这个MVP技术栈方案基于以下设计原则：

1. **快速开发**: 选择Next.js + Node.js统一技术栈，减少学习成本
2. **成本控制**: 使用PostgreSQL + Redis的经典组合，部署灵活
3. **功能完整**: 覆盖DPPaaS平台的所有核心业务需求
4. **扩展性**: 为后续微服务拆分预留空间
5. **合规性**: 严格按照欧盟ESPR法规要求实现

总开发周期6周，能够快速构建出符合过审要求的DPPaaS平台MVP。

### GitHub Actions CI/CD流水线
```yaml
# .github/workflows/deploy.yml
name: DPPaaS Deploy Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      # Go微服务测试
      - name: Test Go Services
        run: |
          cd services
          go mod tidy
          go test ./...

      # Nuxt.js前端测试
      - name: Test Frontend
        run: |
          cd frontend
          npm ci
          npm run test
          npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      # 部署到AWS ECS Fargate
      - name: Deploy to AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          terraform init
          terraform plan
          terraform apply -auto-approve
```

## 明确的开发计划

### 第一阶段（4周）- 基础架构搭建

#### Week 1: 项目初始化
- **Go微服务框架搭建**（2天）
  - 设置Go项目结构，配置Gin框架
  - 实现基础中间件（日志、CORS、认证）
  - 配置gRPC服务间通信

- **Nuxt.js前端初始化**（2天）
  - 创建Nuxt 3项目，配置Tailwind CSS
  - 设置Pinia状态管理
  - 实现基础布局和路由

- **数据库设计实现**（1天）
  - 使用GORM实现数据模型
  - 创建数据库迁移脚本
  - 配置Aurora PostgreSQL连接

#### Week 2-3: 核心服务开发
- **passport-service开发**（5天）
  - 实现DPP CRUD操作
  - JSON-LD格式生成
  - ECDSA数字签名集成

- **registry-service开发**（4天）
  - 产品注册管理
  - UID生成算法
  - 查询接口实现

- **auth-service + Keycloak集成**（3天）
  - Keycloak配置和集成
  - JWT令牌验证
  - RBAC权限控制

#### Week 4: 前端核心功能
- **经营者管理界面**（3天）
  - EORI号码注册表单
  - 经营者信息管理
  - 权限控制界面

- **产品管理界面**（2天）
  - 产品注册向导
  - 40个核心字段表单
  - 产品列表和搜索

### 第二阶段（3周）- 核心业务功能

#### Week 5: DPP生成系统
- **DPP生成引擎**（4天）
  - 完整的JSON-LD格式实现
  - 数字签名服务完善
  - 数据完整性验证

- **载体生成服务**（3天）
  - 二维码生成（支持GS1数字连接）
  - NFC数据生成
  - 载体管理界面

#### Week 6: VC管理系统
- **compliance-service开发**（4天）
  - VC上传和验证
  - BitstringStatusList2021状态管理
  - 第三方机构集成接口

- **VC管理界面**（3天）
  - VC上传界面
  - VC状态监控
  - 验证结果展示

#### Week 7: 查询和集成
- **海关查询接口**（3天）
  - 公开查询API
  - 限流和安全控制
  - 查询结果缓存

- **DPP Registry集成**（4天）
  - 与欧委会系统对接
  - 数据同步机制
  - 错误处理和重试

### 第三阶段（2周）- 部署和测试

#### Week 8: 生产部署
- **AWS基础设施部署**（3天）
  - Terraform脚本完善
  - ECS Fargate服务部署
  - Aurora和MSK配置

- **监控和告警**（2天）
  - CloudWatch监控配置
  - 告警规则设置
  - 日志聚合配置

#### Week 9: 测试和优化
- **端到端测试**（3天）
  - 完整业务流程测试
  - 性能压力测试
  - 安全渗透测试

- **用户验收测试**（2天）
  - 用户界面优化
  - 业务流程验证
  - 文档完善

## 总结

这个技术栈和产品架构方案完全基于概括文档和软件技术文档的要求设计：

### 核心特点
1. **完整的业务覆盖**: 从经营者注册到海关查询的全流程支持
2. **明确的技术栈**: Nuxt.js + Go微服务 + Aurora + Kafka + Keycloak
3. **符合欧盟标准**: JSON-LD格式、ECDSA_P256_SHA256签名、VC管理
4. **生产级架构**: AWS Fargate + Aurora Serverless v2 + MSK Serverless
5. **详细的实现方案**: 包含数据模型、API设计、部署配置

### 开发周期
- **总计9周**: 4周基础架构 + 3周核心功能 + 2周部署测试
- **可扩展性**: 微服务架构便于后续功能扩展
- **合规性**: 严格按照ESPR法规要求实现

这个方案能够快速构建出符合过审要求的DPPaaS平台MVP，同时为后续的功能扩展和规模化运营奠定坚实基础。
