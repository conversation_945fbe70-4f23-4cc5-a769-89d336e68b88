#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Markdown转PDF工具
使用更好的解析和排版，生成高质量PDF
"""

import os
import sys
from pathlib import Path
import markdown
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, KeepTogether
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape
from bs4 import BeautifulSoup

def setup_fonts():
    """设置中文字体"""
    try:
        # Windows字体路径
        font_paths = [
            (r'C:\Windows\Fonts\msyh.ttc', 'Microsoft-YaHei'),
            (r'C:\Windows\Fonts\msyhbd.ttc', 'Microsoft-YaHei-Bold'),
            (r'C:\Windows\Fonts\simsun.ttc', 'SimSun'),
            (r'C:\Windows\Fonts\simhei.ttf', 'SimHei'),
        ]
        
        registered_fonts = []
        for font_path, font_name in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    registered_fonts.append(font_name)
                except Exception as e:
                    print(f"⚠️ 字体注册失败 {font_name}: {e}")
                    continue
        
        if registered_fonts:
            print(f"✅ 成功注册字体: {', '.join(registered_fonts)}")
            return registered_fonts[0]  # 返回第一个成功注册的字体
        else:
            print("⚠️ 未找到中文字体，将使用默认字体")
            return None
            
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")
        return None

def create_enhanced_styles(chinese_font=None):
    """创建增强的PDF样式"""
    styles = getSampleStyleSheet()
    
    base_font = chinese_font if chinese_font else 'Helvetica'
    
    # 文档标题
    styles.add(ParagraphStyle(
        name='DocumentTitle',
        parent=styles['Title'],
        fontName=base_font,
        fontSize=24,
        spaceAfter=30,
        spaceBefore=20,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#1a365d'),
        borderWidth=2,
        borderColor=colors.HexColor('#3182ce'),
        borderPadding=10
    ))
    
    # 一级标题
    styles.add(ParagraphStyle(
        name='CustomHeading1',
        parent=styles['Heading1'],
        fontName=base_font,
        fontSize=18,
        spaceAfter=15,
        spaceBefore=25,
        textColor=colors.HexColor('#2d3748'),
        borderWidth=1,
        borderColor=colors.HexColor('#e2e8f0'),
        borderPadding=8,
        backColor=colors.HexColor('#f7fafc')
    ))

    # 二级标题
    styles.add(ParagraphStyle(
        name='CustomHeading2',
        parent=styles['Heading2'],
        fontName=base_font,
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.HexColor('#2d3748'),
        leftIndent=10
    ))

    # 三级标题
    styles.add(ParagraphStyle(
        name='CustomHeading3',
        parent=styles['Heading3'],
        fontName=base_font,
        fontSize=14,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.HexColor('#4a5568'),
        leftIndent=20
    ))
    
    # 四级标题
    styles.add(ParagraphStyle(
        name='CustomHeading4',
        fontName=base_font,
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.HexColor('#718096'),
        leftIndent=30
    ))
    
    # 正文
    styles.add(ParagraphStyle(
        name='BodyText',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=11,
        spaceAfter=8,
        spaceBefore=2,
        alignment=TA_JUSTIFY,
        leading=16,
        leftIndent=0,
        rightIndent=0
    ))
    
    # 列表项
    styles.add(ParagraphStyle(
        name='ListItem',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=11,
        spaceAfter=4,
        spaceBefore=2,
        leftIndent=20,
        bulletIndent=10,
        leading=14
    ))
    
    # 代码块
    styles.add(ParagraphStyle(
        name='CodeBlock',
        parent=styles['Code'],
        fontName='Courier',
        fontSize=10,
        spaceAfter=12,
        spaceBefore=12,
        backColor=colors.HexColor('#f7fafc'),
        borderColor=colors.HexColor('#e2e8f0'),
        borderWidth=1,
        borderPadding=10,
        leftIndent=10,
        rightIndent=10
    ))
    
    # 引用块
    styles.add(ParagraphStyle(
        name='BlockQuote',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=11,
        spaceAfter=10,
        spaceBefore=10,
        leftIndent=30,
        rightIndent=30,
        backColor=colors.HexColor('#f0fff4'),
        borderColor=colors.HexColor('#38a169'),
        borderWidth=0,
        borderPadding=10,
        leading=14
    ))
    
    return styles

def parse_markdown_enhanced(md_content):
    """增强的Markdown解析"""
    # 配置markdown扩展
    extensions = [
        'markdown.extensions.tables',
        'markdown.extensions.fenced_code',
        'markdown.extensions.codehilite',
        'markdown.extensions.toc',
        'markdown.extensions.extra',
        'markdown.extensions.nl2br'
    ]
    
    # 转换markdown到html
    md = markdown.Markdown(extensions=extensions)
    html_content = md.convert(md_content)
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    return soup

def soup_to_pdf_elements(soup, styles):
    """将BeautifulSoup对象转换为PDF元素"""
    elements = []
    
    def process_element(element, parent_style='BodyText'):
        """递归处理HTML元素"""
        if element.name is None:  # 文本节点
            text = element.string
            if text and text.strip():
                return [Paragraph(text.strip(), styles[parent_style])]
            return []
        
        result = []
        
        # 处理标题
        if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            level = int(element.name[1])
            style_name = f'CustomHeading{min(level, 4)}'
            text = element.get_text().strip()
            if text:
                result.append(Paragraph(text, styles[style_name]))
                result.append(Spacer(1, 5*mm))
        
        # 处理段落
        elif element.name == 'p':
            text = element.get_text().strip()
            if text:
                result.append(Paragraph(text, styles['BodyText']))
                result.append(Spacer(1, 3*mm))
        
        # 处理列表
        elif element.name in ['ul', 'ol']:
            for li in element.find_all('li', recursive=False):
                text = li.get_text().strip()
                if text:
                    bullet = '•' if element.name == 'ul' else f"{len(result)//2 + 1}."
                    result.append(Paragraph(f"{bullet} {text}", styles['ListItem']))
            result.append(Spacer(1, 5*mm))
        
        # 处理表格
        elif element.name == 'table':
            table_data = []
            
            # 处理表头
            thead = element.find('thead')
            if thead:
                for row in thead.find_all('tr'):
                    row_data = []
                    for cell in row.find_all(['th', 'td']):
                        row_data.append(cell.get_text().strip())
                    if row_data:
                        table_data.append(row_data)
            
            # 处理表体
            tbody = element.find('tbody') or element
            for row in tbody.find_all('tr'):
                row_data = []
                for cell in row.find_all(['td', 'th']):
                    cell_text = cell.get_text().strip()
                    # 处理换行
                    cell_text = cell_text.replace('\n', '<br/>')
                    row_data.append(cell_text)
                if row_data:
                    table_data.append(row_data)
            
            if table_data:
                # 计算列宽
                if table_data:
                    col_count = len(table_data[0])
                    col_widths = [A4[0] / col_count * 0.8] * col_count
                    
                    table = Table(table_data, colWidths=col_widths)
                    table.setStyle(TableStyle([
                        # 表头样式
                        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4a5568')),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('TOPPADDING', (0, 0), (-1, 0), 12),
                        
                        # 表体样式
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f7fafc')]),
                        ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e2e8f0')),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 8),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                        ('TOPPADDING', (0, 1), (-1, -1), 8),
                        ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
                    ]))
                    
                    result.append(KeepTogether(table))
                    result.append(Spacer(1, 10*mm))
        
        # 处理代码块
        elif element.name == 'pre':
            code_text = element.get_text()
            if code_text.strip():
                result.append(Paragraph(code_text, styles['CodeBlock']))
                result.append(Spacer(1, 5*mm))
        
        # 处理引用
        elif element.name == 'blockquote':
            text = element.get_text().strip()
            if text:
                result.append(Paragraph(f"❝ {text}", styles['BlockQuote']))
                result.append(Spacer(1, 5*mm))
        
        # 递归处理子元素
        else:
            for child in element.children:
                result.extend(process_element(child, parent_style))
        
        return result
    
    # 处理所有顶级元素
    for element in soup.children:
        elements.extend(process_element(element))
    
    return elements

def convert_md_to_pdf_enhanced(md_file_path, output_dir="pdfs"):
    """增强版Markdown转PDF"""
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 读取markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {md_file_path}: {e}")
        return False
    
    # 获取文件名（不含扩展名）
    file_name = Path(md_file_path).stem
    
    # 输出PDF路径
    pdf_path = Path(output_dir) / f"{file_name}.pdf"
    
    try:
        # 设置字体
        chinese_font = setup_fonts()
        
        # 创建增强样式
        styles = create_enhanced_styles(chinese_font)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            str(pdf_path),
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=25*mm,
            bottomMargin=25*mm,
            title=file_name
        )
        
        # 解析markdown内容
        soup = parse_markdown_enhanced(md_content)
        
        # 转换为PDF元素
        elements = soup_to_pdf_elements(soup, styles)
        
        # 添加文档标题
        elements.insert(0, Paragraph(file_name, styles['DocumentTitle']))
        elements.insert(1, Spacer(1, 15*mm))
        
        # 生成PDF
        doc.build(elements)
        
        print(f"✅ 转换成功: {md_file_path} -> {pdf_path}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败 {md_file_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始增强版MVP文档转PDF...")
    
    # 需要转换的文件列表
    mvp_files = [
        "docs/MVP技术栈设计方案.md",
        "docs/MVP系统架构详细设计.md", 
        "docs/MVP开发实施计划.md"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for file_path in mvp_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到可转换的文件")
        return
    
    # 转换文件
    success_count = 0
    total_count = len(existing_files)
    
    for file_path in existing_files:
        if convert_md_to_pdf_enhanced(file_path, "enhanced_pdfs"):
            success_count += 1
    
    # 输出结果
    print(f"\n📊 转换完成: {success_count}/{total_count} 个文件成功转换")
    
    if success_count > 0:
        print(f"📁 增强版PDF文件保存在: enhanced_pdfs/ 目录下")
        print("\n生成的PDF文件:")
        for file_path in existing_files:
            file_name = Path(file_path).stem
            print(f"  - enhanced_pdfs/{file_name}.pdf")

if __name__ == "__main__":
    main()
