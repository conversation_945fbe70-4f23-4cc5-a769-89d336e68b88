# DPPaaS MVP系统架构详细设计

## 架构概览

### 系统分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  Next.js Frontend Application                              │
│  ├── 经营者门户 (Business Portal)                           │
│  ├── 检测机构门户 (Testing Agency Portal)                   │
│  ├── 公共查询界面 (Public Query Interface)                  │
│  └── 管理后台 (Admin Dashboard)                            │
└─────────────────────────────────────────────────────────────┘
                              │ HTTPS/REST API
┌─────────────────────────────────────────────────────────────┐
│                   应用服务层 (Service Layer)                 │
├─────────────────────────────────────────────────────────────┤
│  Express.js Backend API                                    │
│  ├── 认证服务 (Auth Service)                               │
│  ├── 护照服务 (Passport Service)                           │
│  ├── 产品服务 (Product Service)                            │
│  ├── 凭证服务 (Credential Service)                         │
│  ├── 注册库服务 (Registry Service)                         │
│  └── 文件服务 (File Service)                               │
└─────────────────────────────────────────────────────────────┘
                              │ SQL/NoSQL
┌─────────────────────────────────────────────────────────────┐
│                    数据持久层 (Data Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ├── PostgreSQL (主数据库)                                  │
│  ├── Redis (缓存 & 会话)                                    │
│  └── Cloudinary (文件存储)                                  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块设计

### 1. 认证授权模块 (Auth Module)

#### 功能职责
- 用户注册、登录、登出
- 多租户权限管理
- JWT Token管理
- 角色权限控制

#### 技术实现
```typescript
// 用户角色定义
enum UserRole {
  SUPER_ADMIN = 'super_admin',
  TENANT_ADMIN = 'tenant_admin',
  BUSINESS_USER = 'business_user',
  TESTING_AGENCY = 'testing_agency',
  PUBLIC_USER = 'public_user'
}

// 权限矩阵
const PERMISSIONS = {
  [UserRole.SUPER_ADMIN]: ['*'],
  [UserRole.TENANT_ADMIN]: ['tenant:*'],
  [UserRole.BUSINESS_USER]: ['passport:create', 'passport:read', 'passport:update'],
  [UserRole.TESTING_AGENCY]: ['credential:create', 'credential:verify'],
  [UserRole.PUBLIC_USER]: ['passport:read:public']
}
```

#### API端点
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/me
POST /api/auth/refresh
```

### 2. 护照管理模块 (Passport Module)

#### 功能职责
- 数字产品护照CRUD操作
- UID生成与管理
- 二维码/NFC生成
- 护照状态管理

#### 数据模型
```typescript
interface Passport {
  id: string;
  uid: string; // 唯一标识符
  productId: string;
  tenantId: string;
  status: PassportStatus;
  passportData: PassportData; // JSON-LD格式
  qrCodeUrl?: string;
  nfcData?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface PassportData {
  '@context': string[];
  id: string;
  type: string[];
  manufacturer: string;
  model: string;
  serialNumber: string;
  materials: Material[];
  carbonFootprint: CarbonFootprint;
  certifications: Certification[];
  recyclingInfo: RecyclingInfo;
}
```

#### API端点
```
GET    /api/passports              # 获取护照列表
POST   /api/passports              # 创建新护照
GET    /api/passports/:id          # 获取护照详情
PUT    /api/passports/:id          # 更新护照
DELETE /api/passports/:id          # 删除护照
POST   /api/passports/:id/qr       # 生成二维码
GET    /api/passports/uid/:uid     # 通过UID查询护照
```

### 3. 产品管理模块 (Product Module)

#### 功能职责
- 产品信息管理
- 产品分类管理
- 批次管理
- 供应链信息

#### 数据模型
```typescript
interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  gtin: string; // Global Trade Item Number
  manufacturer: string;
  tenantId: string;
  batchInfo?: BatchInfo;
  specifications: ProductSpecification;
  createdAt: Date;
  updatedAt: Date;
}

interface ProductSpecification {
  dimensions: Dimensions;
  weight: number;
  materials: Material[];
  energyRating?: string;
  safetyStandards: string[];
}
```

### 4. 凭证管理模块 (Credential Module)

#### 功能职责
- VC（可验证凭证）管理
- 数字签名验证
- 凭证状态管理
- 第三方机构集成

#### 数据模型
```typescript
interface VerifiableCredential {
  id: string;
  passportId: string;
  type: CredentialType;
  issuer: string; // DID或机构标识
  issuanceDate: Date;
  expirationDate?: Date;
  credentialSubject: any; // 凭证内容
  proof: DigitalProof;
  status: CredentialStatus;
  createdAt: Date;
}

interface DigitalProof {
  type: string;
  created: Date;
  verificationMethod: string;
  proofPurpose: string;
  proofValue: string;
}
```

### 5. 注册库集成模块 (Registry Module)

#### 功能职责
- DPP Registry对接
- UID注册与查询
- 数据同步
- 海关接口

#### API设计
```typescript
// 注册护照到中央库
POST /api/registry/register
{
  uid: string;
  dppEndpoint: string;
  dataCarrierDigest: string;
  serviceProviderId: string;
  economicOperatorEORI: string;
}

// 查询护照信息
GET /api/registry/lookup/:uid
Response: {
  uid: string;
  dppEndpoint: string;
  isValid: boolean;
  lastUpdated: Date;
}
```

## 数据库设计

### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role user_role NOT NULL,
  tenant_id UUID REFERENCES tenants(id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 租户表
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  type tenant_type NOT NULL, -- business, testing_agency
  eori_number VARCHAR(50),
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 产品表
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  gtin VARCHAR(14),
  category VARCHAR(100) NOT NULL,
  manufacturer VARCHAR(255) NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  specifications JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 护照表
CREATE TABLE passports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  uid VARCHAR(255) UNIQUE NOT NULL,
  product_id UUID NOT NULL REFERENCES products(id),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  status passport_status DEFAULT 'draft',
  passport_data JSONB NOT NULL,
  qr_code_url VARCHAR(500),
  nfc_data TEXT,
  registry_status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 凭证表
CREATE TABLE credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  passport_id UUID NOT NULL REFERENCES passports(id),
  type credential_type NOT NULL,
  issuer VARCHAR(255) NOT NULL,
  issuance_date TIMESTAMP NOT NULL,
  expiration_date TIMESTAMP,
  credential_data JSONB NOT NULL,
  proof JSONB NOT NULL,
  status credential_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 审计日志表
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_type VARCHAR(50) NOT NULL,
  entity_id UUID NOT NULL,
  action VARCHAR(50) NOT NULL,
  user_id UUID REFERENCES users(id),
  changes JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 索引优化
```sql
-- 性能优化索引
CREATE INDEX idx_passports_uid ON passports(uid);
CREATE INDEX idx_passports_tenant_id ON passports(tenant_id);
CREATE INDEX idx_passports_status ON passports(status);
CREATE INDEX idx_credentials_passport_id ON credentials(passport_id);
CREATE INDEX idx_credentials_status ON credentials(status);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## API设计规范

### RESTful API约定
```
GET    /api/v1/resource          # 获取资源列表
POST   /api/v1/resource          # 创建新资源
GET    /api/v1/resource/:id      # 获取单个资源
PUT    /api/v1/resource/:id      # 更新资源
DELETE /api/v1/resource/:id      # 删除资源
```

### 响应格式标准
```typescript
// 成功响应
interface ApiResponse<T> {
  success: true;
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

// 错误响应
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

### 分页和过滤
```typescript
// 查询参数
interface QueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filter?: Record<string, any>;
  search?: string;
}
```

## 安全设计

### 认证安全
- JWT Token + Refresh Token机制
- 密码加密存储 (bcrypt)
- 登录失败限制
- 会话超时管理

### 数据安全
- 数据传输加密 (HTTPS)
- 敏感数据加密存储
- SQL注入防护
- XSS防护

### 权限控制
- 基于角色的访问控制 (RBAC)
- 多租户数据隔离
- API访问频率限制
- 审计日志记录

## 性能优化

### 缓存策略
```typescript
// Redis缓存配置
const cacheConfig = {
  passport: { ttl: 3600 }, // 1小时
  user: { ttl: 1800 },     // 30分钟
  registry: { ttl: 7200 }  // 2小时
};
```

### 数据库优化
- 连接池管理
- 查询优化
- 索引策略
- 分页查询

### 前端优化
- 代码分割
- 图片懒加载
- CDN加速
- 缓存策略

## 监控和日志

### 应用监控
- 性能指标监控
- 错误率监控
- 用户行为分析
- 系统资源监控

### 日志管理
- 结构化日志
- 日志级别管理
- 日志聚合分析
- 安全事件记录

这个详细的系统架构设计为MVP开发提供了完整的技术蓝图，确保系统的可扩展性、安全性和性能。
