# DPPaaS MVP系统架构详细设计

## 系统概述

### 业务背景
DPPaaS (Digital Product Passport as a Service) 是一个数字产品护照管理平台，为企业提供产品全生命周期的数字化追踪和管理服务。系统支持产品信息管理、数字护照生成、可验证凭证(VC)管理和多租户权限控制。

### 核心功能模块
1. **用户认证与权限管理**: 多租户、多角色的权限控制系统
2. **产品信息管理**: 产品基础信息、规格参数、供应链信息管理
3. **数字护照管理**: 护照创建、编辑、状态管理、UID生成
4. **可验证凭证管理**: VC创建、验证、状态跟踪、数字签名
5. **公共查询服务**: 通过UID或二维码查询护照信息
6. **注册库集成**: 与欧盟DPP Registry的数据同步和查询

## 系统架构设计

### 整体架构图

**四层架构模式**

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户访问层                                │
├─────────────────────────────────────────────────────────────────┤
│  Web浏览器  │  移动设备  │  API客户端  │  第三方系统  │  扫码设备  │
└─────────────────────────────────────────────────────────────────┘
                                │
                         HTTPS / REST API
                                │
┌─────────────────────────────────────────────────────────────────┐
│                        前端应用层                                │
├─────────────────────────────────────────────────────────────────┤
│                     Next.js 14 应用程序                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 经营者门户   │ │ 检测机构门户 │ │ 管理员后台   │ │ 公共查询页面 │ │
│  │ - 产品管理   │ │ - 凭证管理   │ │ - 用户管理   │ │ - 护照查询   │ │
│  │ - 护照创建   │ │ - 证书上传   │ │ - 系统配置   │ │ - 信息展示   │ │
│  │ - 数据统计   │ │ - 验证服务   │ │ - 审计日志   │ │ - 二维码扫描 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                         REST API / JSON
                                │
┌─────────────────────────────────────────────────────────────────┐
│                        后端服务层                                │
├─────────────────────────────────────────────────────────────────┤
│                   Express.js API 服务器                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 认证授权服务 │ │ 产品管理服务 │ │ 护照管理服务 │ │ 凭证管理服务 │ │
│  │ - 用户认证   │ │ - CRUD操作   │ │ - 护照生成   │ │ - VC创建     │ │
│  │ - 权限控制   │ │ - 数据验证   │ │ - UID管理    │ │ - 数字签名   │ │
│  │ - JWT管理    │ │ - 文件上传   │ │ - 二维码生成 │ │ - 状态跟踪   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 查询服务     │ │ 注册库服务   │ │ 文件服务     │ │ 审计日志服务 │ │
│  │ - 公共查询   │ │ - 数据同步   │ │ - 文件管理   │ │ - 操作记录   │ │
│  │ - 缓存管理   │ │ - Registry   │ │ - 图片处理   │ │ - 安全审计   │ │
│  │ - 搜索功能   │ │ - API集成    │ │ - CDN分发    │ │ - 合规报告   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                    SQL / Cache / File Storage
                                │
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ PostgreSQL  │ │    Redis    │ │ Cloudinary  │ │   监控日志   │ │
│  │ 主数据库     │ │ 缓存&会话    │ │ 文件存储     │ │ 系统监控     │ │
│  │ - 用户数据   │ │ - 会话存储   │ │ - 图片文件   │ │ - 性能指标   │ │
│  │ - 产品信息   │ │ - 查询缓存   │ │ - 文档文件   │ │ - 错误日志   │ │
│  │ - 护照数据   │ │ - 临时数据   │ │ - 二维码     │ │ - 访问统计   │ │
│  │ - 凭证记录   │ │ - 权限缓存   │ │ - 证书文件   │ │ - 安全事件   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 架构特点

#### 分层设计优势
1. **职责分离**: 每层专注特定功能，降低耦合度
2. **可扩展性**: 各层可独立扩展和优化
3. **可维护性**: 清晰的层次结构便于维护和调试
4. **可测试性**: 分层设计便于单元测试和集成测试

#### 技术选型理由
1. **前端**: Next.js 14提供SSR/SSG、路由管理、性能优化
2. **后端**: Express.js轻量级、灵活、生态丰富
3. **数据库**: PostgreSQL提供ACID事务、JSON支持、高性能
4. **缓存**: Redis提供高性能缓存、会话存储、队列功能

## 核心模块详细设计

### 1. 用户认证与权限管理模块

#### 模块概述
负责整个系统的用户身份认证、权限控制和多租户管理。采用基于角色的访问控制(RBAC)模型，支持细粒度的权限管理。

#### 核心功能
1. **用户管理**: 用户注册、登录、密码管理、账户状态管理
2. **多租户支持**: 租户创建、配置、数据隔离
3. **角色权限**: 角色定义、权限分配、权限验证
4. **会话管理**: JWT Token生成、刷新、过期处理

#### 用户角色体系

**角色层次结构**

| 角色名称 | 英文标识 | 权限范围 | 主要职责 |
|----------|----------|----------|----------|
| **超级管理员** | SUPER_ADMIN | 全系统权限 | 系统配置、用户管理、数据维护 |
| **租户管理员** | TENANT_ADMIN | 租户内全权限 | 租户配置、用户管理、业务管理 |
| **经营者用户** | BUSINESS_USER | 产品和护照管理 | 产品录入、护照创建、数据查看 |
| **检测机构** | TESTING_AGENCY | 凭证管理权限 | 证书上传、凭证验证、状态更新 |
| **公共用户** | PUBLIC_USER | 只读查询权限 | 护照查询、信息浏览 |

#### 权限控制矩阵

**详细权限定义**

| 功能模块 | 超级管理员 | 租户管理员 | 经营者用户 | 检测机构 | 公共用户 |
|----------|------------|------------|------------|----------|----------|
| **用户管理** | ✅ 全部 | ✅ 租户内 | ❌ | ❌ | ❌ |
| **产品管理** | ✅ 全部 | ✅ 租户内 | ✅ 自己的 | ❌ | ❌ |
| **护照创建** | ✅ 全部 | ✅ 租户内 | ✅ 自己的 | ❌ | ❌ |
| **护照查看** | ✅ 全部 | ✅ 租户内 | ✅ 自己的 | ✅ 相关的 | ✅ 公开的 |
| **凭证管理** | ✅ 全部 | ✅ 租户内 | ❌ | ✅ 自己的 | ❌ |
| **系统配置** | ✅ 全部 | ✅ 租户配置 | ❌ | ❌ | ❌ |

#### 技术实现方案

**认证流程**
1. 用户提交登录凭据(邮箱+密码)
2. 服务器验证用户身份和状态
3. 生成JWT Access Token(15分钟有效)
4. 生成Refresh Token(7天有效)
5. 返回Token和用户信息给客户端

**权限验证流程**
1. 客户端请求携带JWT Token
2. 中间件验证Token有效性
3. 解析用户角色和租户信息
4. 检查用户对资源的访问权限
5. 允许或拒绝请求

#### API接口设计

**认证相关接口**

| 方法 | 路径 | 功能 | 参数 |
|------|------|------|------|
| POST | /api/auth/register | 用户注册 | email, password, name, role |
| POST | /api/auth/login | 用户登录 | email, password |
| POST | /api/auth/logout | 用户登出 | - |
| GET | /api/auth/me | 获取当前用户信息 | - |
| POST | /api/auth/refresh | 刷新Token | refreshToken |
| PUT | /api/auth/password | 修改密码 | oldPassword, newPassword |

**权限管理接口**

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| GET | /api/users | 获取用户列表 | ADMIN |
| POST | /api/users | 创建用户 | ADMIN |
| PUT | /api/users/:id | 更新用户信息 | ADMIN |
| DELETE | /api/users/:id | 删除用户 | SUPER_ADMIN |
| GET | /api/tenants | 获取租户列表 | SUPER_ADMIN |
| POST | /api/tenants | 创建租户 | SUPER_ADMIN |

### 2. 数字护照管理模块

#### 模块概述
数字护照管理模块是系统的核心业务模块，负责数字产品护照的完整生命周期管理。支持JSON-LD格式的护照数据、UID生成、二维码生成和状态跟踪。

#### 核心功能
1. **护照创建**: 基于产品信息生成数字护照
2. **UID管理**: 全局唯一标识符生成和验证
3. **数据管理**: JSON-LD格式的护照数据存储和更新
4. **二维码生成**: 护照查询二维码的生成和管理
5. **状态跟踪**: 护照生命周期状态管理
6. **版本控制**: 护照数据的版本历史管理

#### 护照数据结构

**核心数据模型**

| 字段名称 | 数据类型 | 必填 | 说明 |
|----------|----------|------|------|
| id | UUID | ✅ | 系统内部唯一标识 |
| uid | String | ✅ | 全局唯一标识符(符合ISO标准) |
| productId | UUID | ✅ | 关联的产品ID |
| tenantId | UUID | ✅ | 所属租户ID |
| status | Enum | ✅ | 护照状态(草稿/活跃/暂停/废弃) |
| passportData | JSON-LD | ✅ | 护照核心数据 |
| qrCodeUrl | String | ❌ | 二维码图片URL |
| nfcData | String | ❌ | NFC标签数据 |
| version | Integer | ✅ | 数据版本号 |
| createdAt | DateTime | ✅ | 创建时间 |
| updatedAt | DateTime | ✅ | 更新时间 |

**JSON-LD护照数据格式**

护照数据采用JSON-LD格式，符合欧盟DPP标准：

```json
{
  "@context": [
    "https://www.w3.org/ns/credentials/v2",
    "https://dpp.europa.eu/context/v1"
  ],
  "id": "urn:uuid:12345678-1234-5678-9012-123456789012",
  "type": ["DigitalProductPassport"],
  "manufacturer": {
    "name": "ACME Manufacturing Co.",
    "identifier": "EORI:GB123456789000",
    "address": "123 Industrial St, London, UK"
  },
  "product": {
    "name": "Smart Phone Model X",
    "model": "SPX-2024",
    "serialNumber": "SN123456789",
    "gtin": "1234567890123",
    "category": "Electronics"
  },
  "materials": [
    {
      "name": "Aluminum",
      "percentage": 45.2,
      "recycled": true,
      "recyclable": true
    },
    {
      "name": "Lithium",
      "percentage": 8.5,
      "recycled": false,
      "recyclable": true
    }
  ],
  "carbonFootprint": {
    "total": 12.5,
    "unit": "kg CO2e",
    "methodology": "ISO 14067:2018"
  },
  "certifications": [
    {
      "type": "CE Marking",
      "issuer": "Notified Body 1234",
      "validUntil": "2027-12-31"
    }
  ],
  "recyclingInfo": {
    "instructions": "Remove battery before disposal",
    "facilities": ["https://recycling.example.com/locations"]
  }
}
```

#### UID生成策略

**UID格式标准**
- 采用ISO 15459标准
- 格式: `iso15459:{issuer}:{identifier}`
- 示例: `iso15459:ACME:SPX2024-001234567`

**生成算法**
1. 租户标识符(4位字母)
2. 产品类别代码(3位数字)
3. 年份(4位数字)
4. 序列号(9位数字)
5. 校验码(1位数字)

#### 状态管理

**护照状态流转**

| 状态 | 英文标识 | 说明 | 可转换状态 |
|------|----------|------|------------|
| **草稿** | DRAFT | 护照创建中，未完成 | ACTIVE, CANCELLED |
| **活跃** | ACTIVE | 护照已发布，正常使用 | SUSPENDED, REVOKED |
| **暂停** | SUSPENDED | 临时暂停使用 | ACTIVE, REVOKED |
| **撤销** | REVOKED | 永久撤销，不可恢复 | - |
| **取消** | CANCELLED | 草稿阶段取消 | - |

#### API接口设计

**护照管理接口**

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| GET | /api/passports | 获取护照列表 | BUSINESS_USER+ |
| POST | /api/passports | 创建新护照 | BUSINESS_USER+ |
| GET | /api/passports/:id | 获取护照详情 | BUSINESS_USER+ |
| PUT | /api/passports/:id | 更新护照信息 | BUSINESS_USER+ |
| DELETE | /api/passports/:id | 删除护照 | TENANT_ADMIN+ |
| POST | /api/passports/:id/publish | 发布护照 | BUSINESS_USER+ |
| POST | /api/passports/:id/suspend | 暂停护照 | TENANT_ADMIN+ |

**公共查询接口**

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| GET | /api/public/passports/:uid | 通过UID查询护照 | PUBLIC |
| GET | /api/public/qr/:qrCode | 通过二维码查询 | PUBLIC |
| GET | /api/public/verify/:uid | 验证护照有效性 | PUBLIC |

### 3. 产品管理模块 (Product Module)

#### 功能职责
- 产品信息管理
- 产品分类管理
- 批次管理
- 供应链信息

#### 数据模型
```typescript
interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  gtin: string; // Global Trade Item Number
  manufacturer: string;
  tenantId: string;
  batchInfo?: BatchInfo;
  specifications: ProductSpecification;
  createdAt: Date;
  updatedAt: Date;
}

interface ProductSpecification {
  dimensions: Dimensions;
  weight: number;
  materials: Material[];
  energyRating?: string;
  safetyStandards: string[];
}
```

### 4. 凭证管理模块 (Credential Module)

#### 功能职责
- VC（可验证凭证）管理
- 数字签名验证
- 凭证状态管理
- 第三方机构集成

#### 数据模型
```typescript
interface VerifiableCredential {
  id: string;
  passportId: string;
  type: CredentialType;
  issuer: string; // DID或机构标识
  issuanceDate: Date;
  expirationDate?: Date;
  credentialSubject: any; // 凭证内容
  proof: DigitalProof;
  status: CredentialStatus;
  createdAt: Date;
}

interface DigitalProof {
  type: string;
  created: Date;
  verificationMethod: string;
  proofPurpose: string;
  proofValue: string;
}
```

### 5. 注册库集成模块 (Registry Module)

#### 功能职责
- DPP Registry对接
- UID注册与查询
- 数据同步
- 海关接口

#### API设计
```typescript
// 注册护照到中央库
POST /api/registry/register
{
  uid: string;
  dppEndpoint: string;
  dataCarrierDigest: string;
  serviceProviderId: string;
  economicOperatorEORI: string;
}

// 查询护照信息
GET /api/registry/lookup/:uid
Response: {
  uid: string;
  dppEndpoint: string;
  isValid: boolean;
  lastUpdated: Date;
}
```

## 数据库设计

### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role user_role NOT NULL,
  tenant_id UUID REFERENCES tenants(id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 租户表
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  type tenant_type NOT NULL, -- business, testing_agency
  eori_number VARCHAR(50),
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 产品表
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  gtin VARCHAR(14),
  category VARCHAR(100) NOT NULL,
  manufacturer VARCHAR(255) NOT NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  specifications JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 护照表
CREATE TABLE passports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  uid VARCHAR(255) UNIQUE NOT NULL,
  product_id UUID NOT NULL REFERENCES products(id),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  status passport_status DEFAULT 'draft',
  passport_data JSONB NOT NULL,
  qr_code_url VARCHAR(500),
  nfc_data TEXT,
  registry_status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 凭证表
CREATE TABLE credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  passport_id UUID NOT NULL REFERENCES passports(id),
  type credential_type NOT NULL,
  issuer VARCHAR(255) NOT NULL,
  issuance_date TIMESTAMP NOT NULL,
  expiration_date TIMESTAMP,
  credential_data JSONB NOT NULL,
  proof JSONB NOT NULL,
  status credential_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 审计日志表
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_type VARCHAR(50) NOT NULL,
  entity_id UUID NOT NULL,
  action VARCHAR(50) NOT NULL,
  user_id UUID REFERENCES users(id),
  changes JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 索引优化
```sql
-- 性能优化索引
CREATE INDEX idx_passports_uid ON passports(uid);
CREATE INDEX idx_passports_tenant_id ON passports(tenant_id);
CREATE INDEX idx_passports_status ON passports(status);
CREATE INDEX idx_credentials_passport_id ON credentials(passport_id);
CREATE INDEX idx_credentials_status ON credentials(status);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## API设计规范

### RESTful API约定
```
GET    /api/v1/resource          # 获取资源列表
POST   /api/v1/resource          # 创建新资源
GET    /api/v1/resource/:id      # 获取单个资源
PUT    /api/v1/resource/:id      # 更新资源
DELETE /api/v1/resource/:id      # 删除资源
```

### 响应格式标准
```typescript
// 成功响应
interface ApiResponse<T> {
  success: true;
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

// 错误响应
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

### 分页和过滤
```typescript
// 查询参数
interface QueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filter?: Record<string, any>;
  search?: string;
}
```

## 安全设计

### 认证安全
- JWT Token + Refresh Token机制
- 密码加密存储 (bcrypt)
- 登录失败限制
- 会话超时管理

### 数据安全
- 数据传输加密 (HTTPS)
- 敏感数据加密存储
- SQL注入防护
- XSS防护

### 权限控制
- 基于角色的访问控制 (RBAC)
- 多租户数据隔离
- API访问频率限制
- 审计日志记录

## 性能优化

### 缓存策略
```typescript
// Redis缓存配置
const cacheConfig = {
  passport: { ttl: 3600 }, // 1小时
  user: { ttl: 1800 },     // 30分钟
  registry: { ttl: 7200 }  // 2小时
};
```

### 数据库优化
- 连接池管理
- 查询优化
- 索引策略
- 分页查询

### 前端优化
- 代码分割
- 图片懒加载
- CDN加速
- 缓存策略

## 监控和日志

### 应用监控
- 性能指标监控
- 错误率监控
- 用户行为分析
- 系统资源监控

### 日志管理
- 结构化日志
- 日志级别管理
- 日志聚合分析
- 安全事件记录

这个详细的系统架构设计为MVP开发提供了完整的技术蓝图，确保系统的可扩展性、安全性和性能。
