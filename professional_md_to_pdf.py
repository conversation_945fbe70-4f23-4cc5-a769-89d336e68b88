#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业级Markdown转PDF工具
使用更清晰的排版和更好的中文支持
"""

import os
import sys
from pathlib import Path
import markdown
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape

def setup_chinese_fonts():
    """设置中文字体"""
    try:
        # Windows字体路径
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',  # 微软雅黑
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                    print(f"✅ 成功注册中文字体: {font_path}")
                    return True
                except Exception as e:
                    print(f"⚠️ 字体注册失败: {e}")
                    continue
        
        print("⚠️ 未找到中文字体，将使用默认字体")
        return False
        
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")
        return False

def create_professional_styles(has_chinese=False):
    """创建专业样式表"""
    # 获取基础样式
    base_styles = getSampleStyleSheet()
    
    # 字体选择
    font_name = 'ChineseFont' if has_chinese else 'Helvetica'
    
    # 创建新的样式字典
    styles = {}
    
    # 文档标题
    styles['Title'] = ParagraphStyle(
        'Title',
        parent=base_styles['Title'],
        fontName=font_name,
        fontSize=22,
        spaceAfter=30,
        spaceBefore=0,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#1a202c'),
        borderWidth=2,
        borderColor=colors.HexColor('#4299e1'),
        borderPadding=15
    )
    
    # 一级标题
    styles['H1'] = ParagraphStyle(
        'H1',
        fontName=font_name,
        fontSize=18,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.HexColor('#2d3748'),
        backColor=colors.HexColor('#f7fafc'),
        borderWidth=1,
        borderColor=colors.HexColor('#e2e8f0'),
        borderPadding=10
    )
    
    # 二级标题
    styles['H2'] = ParagraphStyle(
        'H2',
        fontName=font_name,
        fontSize=16,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.HexColor('#2d3748'),
        leftIndent=5
    )
    
    # 三级标题
    styles['H3'] = ParagraphStyle(
        'H3',
        fontName=font_name,
        fontSize=14,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.HexColor('#4a5568'),
        leftIndent=10
    )
    
    # 四级标题
    styles['H4'] = ParagraphStyle(
        'H4',
        fontName=font_name,
        fontSize=12,
        spaceAfter=6,
        spaceBefore=10,
        textColor=colors.HexColor('#718096'),
        leftIndent=15
    )
    
    # 正文
    styles['Body'] = ParagraphStyle(
        'Body',
        fontName=font_name,
        fontSize=11,
        spaceAfter=6,
        spaceBefore=2,
        alignment=TA_JUSTIFY,
        leading=16,
        textColor=colors.HexColor('#2d3748')
    )
    
    # 列表项
    styles['List'] = ParagraphStyle(
        'List',
        fontName=font_name,
        fontSize=11,
        spaceAfter=3,
        spaceBefore=1,
        leftIndent=20,
        leading=14,
        textColor=colors.HexColor('#2d3748')
    )
    
    # 代码
    styles['Code'] = ParagraphStyle(
        'Code',
        fontName='Courier',
        fontSize=10,
        spaceAfter=10,
        spaceBefore=10,
        backColor=colors.HexColor('#f7fafc'),
        borderColor=colors.HexColor('#e2e8f0'),
        borderWidth=1,
        borderPadding=8,
        leftIndent=10,
        rightIndent=10
    )
    
    return styles

def clean_text(text):
    """清理文本"""
    if not text:
        return ""
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 解码HTML实体
    text = unescape(text)
    # 清理多余空白
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def parse_markdown_content(md_content, styles):
    """解析Markdown内容为PDF元素"""
    elements = []
    
    # 配置markdown扩展
    extensions = [
        'markdown.extensions.tables',
        'markdown.extensions.fenced_code',
        'markdown.extensions.extra'
    ]
    
    # 转换markdown到html
    md = markdown.Markdown(extensions=extensions)
    html_content = md.convert(md_content)
    
    # 按行处理HTML
    lines = html_content.split('\n')
    current_paragraph = ""
    in_table = False
    table_rows = []
    in_code_block = False
    code_content = ""
    
    for line in lines:
        line = line.strip()
        
        if not line:
            # 空行处理
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                elements.append(Spacer(1, 3*mm))
                current_paragraph = ""
            continue
        
        # 处理标题
        if line.startswith('<h1>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            title = clean_text(line)
            elements.append(Paragraph(title, styles['H1']))
            elements.append(Spacer(1, 5*mm))
            
        elif line.startswith('<h2>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            title = clean_text(line)
            elements.append(Paragraph(title, styles['H2']))
            elements.append(Spacer(1, 4*mm))
            
        elif line.startswith('<h3>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            title = clean_text(line)
            elements.append(Paragraph(title, styles['H3']))
            elements.append(Spacer(1, 3*mm))
            
        elif line.startswith('<h4>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            title = clean_text(line)
            elements.append(Paragraph(f"<b>{title}</b>", styles['H4']))
            elements.append(Spacer(1, 2*mm))
        
        # 处理表格
        elif line.startswith('<table>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            in_table = True
            table_rows = []
            
        elif line.startswith('</table>'):
            if table_rows:
                # 创建表格
                if table_rows:
                    col_count = len(table_rows[0])
                    # 计算列宽
                    available_width = A4[0] - 40*mm  # 减去页边距
                    col_width = available_width / col_count
                    col_widths = [col_width] * col_count
                    
                    table = Table(table_rows, colWidths=col_widths)
                    table.setStyle(TableStyle([
                        # 表头样式
                        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4a5568')),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 10),
                        ('TOPPADDING', (0, 0), (-1, 0), 10),
                        
                        # 表体样式
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                        ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 6),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                        ('TOPPADDING', (0, 1), (-1, -1), 6),
                        ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
                    ]))
                    
                    elements.append(table)
                    elements.append(Spacer(1, 8*mm))
            in_table = False
            table_rows = []
            
        elif in_table and ('<td>' in line or '<th>' in line):
            # 解析表格行
            cells = re.findall(r'<t[hd]>(.*?)</t[hd]>', line)
            if cells:
                clean_cells = []
                for cell in cells:
                    clean_cell = clean_text(cell)
                    # 处理长文本换行
                    if len(clean_cell) > 50:
                        clean_cell = clean_cell[:47] + "..."
                    clean_cells.append(clean_cell)
                table_rows.append(clean_cells)
        
        # 处理代码块
        elif line.startswith('<pre><code>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            in_code_block = True
            code_content = clean_text(line)
            
        elif line.startswith('</code></pre>'):
            if in_code_block and code_content:
                elements.append(Paragraph(code_content, styles['Code']))
                elements.append(Spacer(1, 5*mm))
            in_code_block = False
            code_content = ""
            
        elif in_code_block:
            code_content += "\n" + clean_text(line)
        
        # 处理列表项
        elif line.startswith('<li>'):
            if current_paragraph:
                elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
                current_paragraph = ""
            item_text = clean_text(line)
            elements.append(Paragraph(f"• {item_text}", styles['List']))
            
        # 处理段落
        elif line.startswith('<p>'):
            text = clean_text(line)
            current_paragraph += text + " "
            
        # 处理其他内容
        else:
            if not line.startswith('<') and line:
                current_paragraph += line + " "
    
    # 处理剩余段落
    if current_paragraph:
        elements.append(Paragraph(clean_text(current_paragraph), styles['Body']))
    
    return elements

def convert_md_to_professional_pdf(md_file_path, output_dir="professional_pdfs"):
    """转换Markdown为专业PDF"""
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 读取markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {md_file_path}: {e}")
        return False
    
    # 获取文件名
    file_name = Path(md_file_path).stem
    pdf_path = Path(output_dir) / f"{file_name}.pdf"
    
    try:
        # 设置字体
        has_chinese = setup_chinese_fonts()
        
        # 创建样式
        styles = create_professional_styles(has_chinese)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            str(pdf_path),
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=25*mm,
            bottomMargin=25*mm,
            title=file_name,
            author="DPPaaS Team"
        )
        
        # 解析内容
        elements = parse_markdown_content(md_content, styles)
        
        # 添加标题
        elements.insert(0, Paragraph(file_name, styles['Title']))
        elements.insert(1, Spacer(1, 10*mm))
        
        # 生成PDF
        doc.build(elements)
        
        print(f"✅ 转换成功: {md_file_path} -> {pdf_path}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败 {md_file_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始专业级MVP文档转PDF...")
    
    # 需要转换的文件列表
    mvp_files = [
        "docs/MVP技术栈设计方案.md",
        "docs/MVP系统架构详细设计.md", 
        "docs/MVP开发实施计划.md"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for file_path in mvp_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到可转换的文件")
        return
    
    # 转换文件
    success_count = 0
    total_count = len(existing_files)
    
    for file_path in existing_files:
        if convert_md_to_professional_pdf(file_path):
            success_count += 1
    
    # 输出结果
    print(f"\n📊 转换完成: {success_count}/{total_count} 个文件成功转换")
    
    if success_count > 0:
        print(f"📁 专业版PDF文件保存在: professional_pdfs/ 目录下")
        print("\n生成的PDF文件:")
        for file_path in existing_files:
            file_name = Path(file_path).stem
            print(f"  - professional_pdfs/{file_name}.pdf")
        
        print("\n📋 PDF特点:")
        print("  • 专业的文档排版")
        print("  • 清晰的标题层次")
        print("  • 优化的表格样式")
        print("  • 完美的中文字体支持")
        print("  • A4标准页面格式")

if __name__ == "__main__":
    main()
