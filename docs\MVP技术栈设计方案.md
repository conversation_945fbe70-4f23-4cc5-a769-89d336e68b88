# DPPaaS MVP技术栈设计方案

## 项目概述

### 项目目标
构建一个数字产品护照(DPP)管理平台的MVP版本，支持产品护照的创建、管理、验证和查询功能。

### 核心业务功能
1. **产品管理**: 产品信息录入、编辑、分类管理
2. **护照生成**: 数字产品护照创建、UID生成、二维码生成
3. **凭证管理**: VC(可验证凭证)创建、验证、状态管理
4. **多租户支持**: 经营者、检测机构、管理员等不同角色
5. **公共查询**: 通过UID或二维码查询产品护照信息
6. **注册库集成**: 与DPP Registry的数据同步

## 设计原则

### MVP核心目标
1. **快速验证**: 2-3个月内完成核心功能验证
2. **成本控制**: 最小化基础设施和开发成本，月运营成本控制在150美元以内
3. **功能聚焦**: 专注核心DPP功能，暂缓高级特性如实时通知、复杂分析等
4. **易于扩展**: 架构设计考虑后续扩展路径，支持微服务化演进

### 技术选型原则
1. **技术栈统一**: 前后端使用相同语言生态，降低学习成本
2. **开发效率优先**: 选择成熟、文档完善的技术方案
3. **类型安全**: 全栈TypeScript，减少运行时错误
4. **云原生**: 优先选择托管服务，减少运维负担

## 技术架构总览

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户层 (Users)                           │
├─────────────────────────────────────────────────────────────────┤
│  经营者    │  检测机构   │  海关人员   │  消费者   │  管理员    │
└─────────────────────────────────────────────────────────────────┘
                                │
                         HTTPS/Web Browser
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    前端应用层 (Frontend)                         │
├─────────────────────────────────────────────────────────────────┤
│                     Next.js 14 Application                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 产品管理页面 │ │ 护照创建页面 │ │ 凭证管理页面 │ │ 公共查询页面 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 用户认证组件 │ │ 表单验证组件 │ │ 数据展示组件 │ │ 文件上传组件 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                         REST API / JSON
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    后端服务层 (Backend)                          │
├─────────────────────────────────────────────────────────────────┤
│                   Express.js API Server                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 认证服务模块 │ │ 产品服务模块 │ │ 护照服务模块 │ │ 凭证服务模块 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 文件服务模块 │ │ 注册库模块   │ │ 权限控制模块 │ │ 日志审计模块 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                         SQL / Cache / Files
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    数据存储层 (Storage)                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ PostgreSQL  │ │    Redis    │ │ Cloudinary  │ │   Backup    │ │
│  │  主数据库    │ │  缓存&会话   │ │  文件存储    │ │   备份存储   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 详细技术栈

### 1. 前端技术栈

#### 核心框架
**Next.js 14 (App Router)**
- **选择理由**: React生态最成熟的全栈框架
- **核心特性**:
  - 内置SSR/SSG支持，SEO友好
  - App Router提供更好的路由管理
  - 内置图片优化和性能优化
  - Vercel部署零配置
- **具体应用**:
  - 多租户门户页面
  - 产品护照展示页面
  - 管理后台界面
  - 公共查询接口

#### UI组件库
**Tailwind CSS + shadcn/ui**
- **选择理由**: 现代化的原子CSS框架 + 高质量组件库
- **核心优势**:
  - 快速开发，无需写CSS
  - 组件库完整，包含表单、表格、对话框等
  - 响应式设计，移动端友好
  - TypeScript支持完善
- **具体组件**:
  - 数据表格(DataTable)
  - 表单组件(Form, Input, Select)
  - 导航组件(Navigation, Breadcrumb)
  - 反馈组件(Toast, Alert, Dialog)

#### 状态管理
**Zustand**
- **选择理由**: 轻量级状态管理库
- **核心优势**:
  - 学习成本低，API简洁
  - TypeScript友好
  - 无需Provider包装
  - 支持中间件扩展
- **应用场景**:
  - 用户认证状态
  - 表单数据管理
  - 全局配置信息
  - 临时UI状态

#### 表单处理
**React Hook Form + Zod**
- **技术组合**: 表单库 + 验证库
- **核心优势**:
  - 性能优秀，减少重渲染
  - 类型安全的验证规则
  - 与TypeScript深度集成
  - 支持复杂表单逻辑
- **应用场景**:
  - 产品信息录入表单
  - 护照创建向导
  - 用户注册登录表单
  - 凭证管理表单

### 2. 后端技术栈

#### 核心框架
**Node.js + Express.js**
- **选择理由**: 与前端技术栈统一，全栈JavaScript
- **核心特性**:
  - 丰富的中间件生态
  - 高性能异步I/O
  - 与前端共享类型定义
  - 开发调试便捷
- **具体应用**:
  - RESTful API服务
  - 文件上传处理
  - 数据库操作
  - 第三方服务集成

#### API设计
**RESTful API + OpenAPI**
- **设计标准**: REST架构风格 + OpenAPI 3.0规范
- **核心优势**:
  - 标准化的API设计
  - 自动生成API文档
  - 前后端协作友好
  - 支持代码生成
- **API结构**:
  - GET /api/products - 获取产品列表
  - POST /api/passports - 创建产品护照
  - PUT /api/credentials/:id - 更新凭证状态
  - GET /api/registry/:uid - 查询护照信息

#### 认证授权
**NextAuth.js**
- **选择理由**: 与Next.js深度集成的认证解决方案
- **核心特性**:
  - 多种认证方式支持
  - JWT和Session双模式
  - 内置安全最佳实践
  - 支持OAuth提供商
- **认证流程**:
  - 邮箱密码登录
  - 多租户权限控制
  - JWT Token管理
  - 会话状态维护

#### 数据验证
**Zod (统一前后端)**
- **选择理由**: TypeScript优先的验证库
- **核心优势**:
  - 类型安全的验证规则
  - 前后端共享Schema
  - 自动类型推导
  - 详细的错误信息
- **验证场景**:
  - API请求参数验证
  - 数据库写入验证
  - 文件格式验证
  - 业务规则验证

### 3. 数据存储技术栈

#### 主数据库
**PostgreSQL**
- **选择理由**: 功能最强大的开源关系型数据库
- **核心特性**:
  - ACID事务保证数据一致性
  - 强大的JSON/JSONB支持
  - 丰富的数据类型和索引
  - 优秀的并发性能
- **数据存储**:
  - 用户和租户信息
  - 产品和护照数据
  - 凭证和审计日志
  - 系统配置信息

#### ORM框架
**Prisma**
- **选择理由**: 现代化的TypeScript优先ORM
- **核心优势**:
  - 类型安全的数据库操作
  - 自动生成TypeScript类型
  - 可视化数据库管理
  - 强大的迁移工具
- **功能特性**:
  - Schema定义和版本管理
  - 查询构建器和关系查询
  - 数据库连接池管理
  - 开发时数据库预览

#### 缓存系统
**Redis**
- **选择理由**: 高性能内存数据库
- **应用场景**:
  - 用户会话存储
  - API响应缓存
  - 频繁查询数据缓存
  - 临时数据存储
- **缓存策略**:
  - 护照查询结果缓存(TTL: 1小时)
  - 用户权限信息缓存(TTL: 30分钟)
  - 产品列表缓存(TTL: 15分钟)
  - 系统配置缓存(TTL: 24小时)

#### 文件存储
**Cloudinary**
- **选择理由**: 专业的云端媒体管理平台
- **核心功能**:
  - 图片自动优化和压缩
  - CDN全球加速
  - 多格式转换支持
  - 安全的文件上传
- **存储内容**:
  - 产品图片和文档
  - 生成的二维码图片
  - 用户上传的证书文件
  - 系统Logo和品牌资源

### 4. 基础设施和部署

#### 前端部署
**Vercel**
- **选择理由**: Next.js官方推荐的部署平台
- **核心优势**:
  - 零配置部署，Git集成
  - 全球CDN和边缘计算
  - 自动HTTPS和域名管理
  - 内置性能监控
- **部署特性**:
  - 自动构建和部署
  - 预览环境支持
  - 环境变量管理
  - 分支部署策略

#### 后端部署
**Railway**
- **选择理由**: 现代化的应用部署平台
- **核心优势**:
  - 简单的容器化部署
  - 自动扩展和负载均衡
  - 内置数据库支持
  - 合理的定价模式
- **部署配置**:
  - Docker容器部署
  - 环境变量配置
  - 健康检查和监控
  - 日志聚合和查看

#### 数据库托管
**Supabase**
- **选择理由**: 开源的Firebase替代方案
- **核心服务**:
  - 托管PostgreSQL数据库
  - 实时数据订阅
  - 内置认证服务
  - 自动备份和恢复
- **管理功能**:
  - 可视化数据库管理
  - SQL编辑器和查询
  - 数据库迁移工具
  - 性能监控面板

#### 监控和日志
**Vercel Analytics + Sentry**
- **监控组合**: 性能监控 + 错误追踪
- **Vercel Analytics**:
  - 页面性能监控
  - 用户行为分析
  - Core Web Vitals追踪
  - 实时访问统计
- **Sentry**:
  - 错误自动捕获和报告
  - 性能问题追踪
  - 用户会话重放
  - 告警和通知集成

### 5. 开发工具链

#### 包管理
**pnpm**
- **选择理由**: 快速、节省磁盘空间的包管理器
- **核心优势**:
  - 速度比npm快2-3倍
  - 磁盘空间节省高达50%
  - 严格的依赖管理
  - Monorepo支持

#### 代码质量
**ESLint + Prettier + Husky**
- **工具组合**: 代码检查 + 格式化 + Git钩子
- **质量保证**:
  - 统一的代码风格
  - 自动格式化
  - 提交前检查
  - TypeScript类型检查

#### 类型检查
**TypeScript**
- **选择理由**: JavaScript的超集，提供静态类型
- **开发优势**:
  - 编译时错误检查
  - 智能代码补全
  - 重构安全性
  - 团队协作效率

#### 测试框架
**Vitest + Testing Library**
- **测试组合**: 快速测试运行器 + 组件测试库
- **测试策略**:
  - 单元测试覆盖核心逻辑
  - 组件测试验证UI行为
  - 集成测试确保API正确性
  - E2E测试验证关键流程

### 开发工具
| 工具 | 选择 | 理由 |
|------|------|------|
| **包管理** | pnpm | • 速度快<br>• 磁盘效率高 |
| **代码质量** | ESLint + Prettier + Husky | • 代码规范<br>• 自动格式化 |
| **类型检查** | TypeScript | • 类型安全<br>• 开发体验 |
| **测试** | Vitest + Testing Library | • 快速执行<br>• 组件测试 |
| **API文档** | Swagger UI | • 自动生成<br>• 交互式文档 |

## 架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │  Express API    │    │  PostgreSQL     │
│                 │◄──►│                 │◄──►│                 │
│ • 用户界面      │    │ • 业务逻辑      │    │ • 数据存储      │
│ • 状态管理      │    │ • API端点       │    │ • 关系数据      │
│ • 路由管理      │    │ • 认证授权      │    │ • JSON字段      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │     Redis       │              │
         └──────────────►│                 │◄─────────────┘
                        │ • 会话存储      │
                        │ • 缓存数据      │
                        │ • 队列任务      │
                        └─────────────────┘
```

### 数据库设计
```sql
-- 核心表结构
Users (用户表)
├── id, email, name, role, tenant_id
├── created_at, updated_at

Tenants (租户表)
├── id, name, type, settings
├── created_at, updated_at

Products (产品表)
├── id, name, description, tenant_id
├── gtin, serial_number, category
├── created_at, updated_at

Passports (护照表)
├── id, product_id, uid, status
├── passport_data (JSON)
├── qr_code_url, nfc_data
├── created_at, updated_at

Credentials (凭证表)
├── id, passport_id, type, issuer
├── credential_data (JSON)
├── signature, verification_method
├── created_at, expires_at
```

### API设计
```
/api/v1/
├── auth/
│   ├── POST /login
│   ├── POST /logout
│   └── GET /me
├── passports/
│   ├── GET /
│   ├── POST /
│   ├── GET /:id
│   ├── PUT /:id
│   └── DELETE /:id
├── products/
│   ├── GET /
│   ├── POST /
│   └── GET /:id
├── credentials/
│   ├── GET /passport/:id/credentials
│   ├── POST /passport/:id/credentials
│   └── PUT /credentials/:id/verify
└── registry/
    ├── GET /lookup/:uid
    └── POST /register
```

## 开发阶段规划

### Phase 1: 基础框架 (2周)
- [ ] 项目初始化和环境配置
- [ ] 数据库设计和迁移
- [ ] 基础认证系统
- [ ] API框架搭建

### Phase 2: 核心功能 (4周)
- [ ] 产品管理功能
- [ ] 护照创建和编辑
- [ ] 二维码生成
- [ ] 基础查询功能

### Phase 3: 高级功能 (3周)
- [ ] VC凭证管理
- [ ] 数字签名验证
- [ ] 多租户支持
- [ ] 国际化

### Phase 4: 集成测试 (2周)
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 安全测试
- [ ] 部署上线

## 成本估算

### 开发成本
- 开发时间：11周 (约3个月)
- 团队规模：2-3人 (全栈开发者)

### 运营成本 (月)
- Vercel Pro: $20
- Railway: $5-20
- Supabase: $25
- Cloudinary: $89
- **总计**: ~$150/月

## 技术债务管理

### 已知限制
1. 单体架构在高并发下的扩展性
2. 简化的认证方案可能不满足企业级需求
3. 缺少实时通知和复杂事件处理

### 迁移路径
1. **微服务化**: 后续可拆分为独立服务
2. **认证升级**: 可迁移到Keycloak或Auth0
3. **消息队列**: 可引入Redis Queue或Kafka
4. **云原生**: 可迁移到AWS/Azure容器化部署

## 总结

这个MVP技术栈在保证核心功能完整性的同时，最大化了开发效率和成本控制。通过现代化的技术选型和合理的架构设计，为后续的扩展和优化奠定了良好基础。
