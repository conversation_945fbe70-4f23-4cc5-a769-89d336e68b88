# DPPaaS MVP技术栈设计方案

## 设计原则

### MVP核心目标
1. **快速验证**: 2-3个月内完成核心功能验证
2. **成本控制**: 最小化基础设施和开发成本
3. **功能聚焦**: 专注核心DPP功能，暂缓高级特性
4. **易于扩展**: 架构设计考虑后续扩展路径

### 简化策略
- 单体应用替代微服务架构
- 关系型数据库替代复杂数据方案
- 简化认证方案
- 减少外部依赖

## MVP技术栈

### 前端技术栈
| 技术 | 选择 | 理由 |
|------|------|------|
| **框架** | Next.js 14 (App Router) | • React生态成熟<br>• SSR/SSG内置<br>• 开发效率高<br>• 社区资源丰富 |
| **UI库** | Tailwind CSS + shadcn/ui | • 快速开发<br>• 组件库完整<br>• 响应式设计 |
| **状态管理** | Zustand | • 轻量级<br>• 学习成本低<br>• TypeScript友好 |
| **表单处理** | React Hook Form + Zod | • 性能优秀<br>• 类型安全<br>• 验证完整 |
| **国际化** | next-intl | • Next.js官方推荐<br>• SSR支持 |

### 后端技术栈
| 技术 | 选择 | 理由 |
|------|------|------|
| **框架** | Node.js + Express.js | • 与前端技术栈统一<br>• 开发效率高<br>• 生态丰富 |
| **API设计** | RESTful API + OpenAPI | • 标准化<br>• 文档自动生成<br>• 前后端协作友好 |
| **认证授权** | NextAuth.js | • 与Next.js深度集成<br>• 多种认证方式<br>• 安全性好 |
| **数据验证** | Joi / Zod | • 类型安全<br>• 前后端共享schema |
| **文件处理** | Multer + Sharp | • 图片处理<br>• PDF处理 |

### 数据库技术栈
| 技术 | 选择 | 理由 |
|------|------|------|
| **主数据库** | PostgreSQL | • ACID事务<br>• JSON支持<br>• 扩展性好 |
| **ORM** | Prisma | • 类型安全<br>• 迁移管理<br>• 开发体验好 |
| **缓存** | Redis | • 会话存储<br>• 缓存加速<br>• 队列支持 |

### 基础设施
| 技术 | 选择 | 理由 |
|------|------|------|
| **部署平台** | Vercel (前端) + Railway (后端) | • 零配置部署<br>• 自动扩展<br>• 成本可控 |
| **数据库托管** | Supabase / PlanetScale | • 托管PostgreSQL<br>• 自动备份<br>• 开发友好 |
| **文件存储** | Cloudinary / Uploadthing | • CDN加速<br>• 图片优化<br>• 简单集成 |
| **监控日志** | Vercel Analytics + Sentry | • 性能监控<br>• 错误追踪<br>• 用户分析 |

### 开发工具
| 工具 | 选择 | 理由 |
|------|------|------|
| **包管理** | pnpm | • 速度快<br>• 磁盘效率高 |
| **代码质量** | ESLint + Prettier + Husky | • 代码规范<br>• 自动格式化 |
| **类型检查** | TypeScript | • 类型安全<br>• 开发体验 |
| **测试** | Vitest + Testing Library | • 快速执行<br>• 组件测试 |
| **API文档** | Swagger UI | • 自动生成<br>• 交互式文档 |

## 架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │  Express API    │    │  PostgreSQL     │
│                 │◄──►│                 │◄──►│                 │
│ • 用户界面      │    │ • 业务逻辑      │    │ • 数据存储      │
│ • 状态管理      │    │ • API端点       │    │ • 关系数据      │
│ • 路由管理      │    │ • 认证授权      │    │ • JSON字段      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │     Redis       │              │
         └──────────────►│                 │◄─────────────┘
                        │ • 会话存储      │
                        │ • 缓存数据      │
                        │ • 队列任务      │
                        └─────────────────┘
```

### 数据库设计
```sql
-- 核心表结构
Users (用户表)
├── id, email, name, role, tenant_id
├── created_at, updated_at

Tenants (租户表)
├── id, name, type, settings
├── created_at, updated_at

Products (产品表)
├── id, name, description, tenant_id
├── gtin, serial_number, category
├── created_at, updated_at

Passports (护照表)
├── id, product_id, uid, status
├── passport_data (JSON)
├── qr_code_url, nfc_data
├── created_at, updated_at

Credentials (凭证表)
├── id, passport_id, type, issuer
├── credential_data (JSON)
├── signature, verification_method
├── created_at, expires_at
```

### API设计
```
/api/v1/
├── auth/
│   ├── POST /login
│   ├── POST /logout
│   └── GET /me
├── passports/
│   ├── GET /
│   ├── POST /
│   ├── GET /:id
│   ├── PUT /:id
│   └── DELETE /:id
├── products/
│   ├── GET /
│   ├── POST /
│   └── GET /:id
├── credentials/
│   ├── GET /passport/:id/credentials
│   ├── POST /passport/:id/credentials
│   └── PUT /credentials/:id/verify
└── registry/
    ├── GET /lookup/:uid
    └── POST /register
```

## 开发阶段规划

### Phase 1: 基础框架 (2周)
- [ ] 项目初始化和环境配置
- [ ] 数据库设计和迁移
- [ ] 基础认证系统
- [ ] API框架搭建

### Phase 2: 核心功能 (4周)
- [ ] 产品管理功能
- [ ] 护照创建和编辑
- [ ] 二维码生成
- [ ] 基础查询功能

### Phase 3: 高级功能 (3周)
- [ ] VC凭证管理
- [ ] 数字签名验证
- [ ] 多租户支持
- [ ] 国际化

### Phase 4: 集成测试 (2周)
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 安全测试
- [ ] 部署上线

## 成本估算

### 开发成本
- 开发时间：11周 (约3个月)
- 团队规模：2-3人 (全栈开发者)

### 运营成本 (月)
- Vercel Pro: $20
- Railway: $5-20
- Supabase: $25
- Cloudinary: $89
- **总计**: ~$150/月

## 技术债务管理

### 已知限制
1. 单体架构在高并发下的扩展性
2. 简化的认证方案可能不满足企业级需求
3. 缺少实时通知和复杂事件处理

### 迁移路径
1. **微服务化**: 后续可拆分为独立服务
2. **认证升级**: 可迁移到Keycloak或Auth0
3. **消息队列**: 可引入Redis Queue或Kafka
4. **云原生**: 可迁移到AWS/Azure容器化部署

## 总结

这个MVP技术栈在保证核心功能完整性的同时，最大化了开发效率和成本控制。通过现代化的技术选型和合理的架构设计，为后续的扩展和优化奠定了良好基础。
