#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Markdown转PDF工具
使用markdown转HTML，然后生成可打印的HTML文件
"""

import os
import sys
from pathlib import Path
import markdown

def markdown_to_html(md_content, title="文档"):
    """将Markdown内容转换为HTML"""
    
    # 配置markdown扩展
    extensions = [
        'markdown.extensions.tables',
        'markdown.extensions.fenced_code',
        'markdown.extensions.codehilite',
        'markdown.extensions.toc',
        'markdown.extensions.extra'
    ]
    
    # 转换markdown到html
    md = markdown.Markdown(extensions=extensions)
    html_content = md.convert(md_content)
    
    # 创建完整的HTML文档
    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
            
            body {{
                font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimSun', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 900px;
                margin: 0 auto;
                padding: 40px 20px;
                background: white;
                font-size: 14px;
            }}
            
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 2em;
                margin-bottom: 1em;
                font-weight: 600;
                page-break-after: avoid;
            }}
            
            h1 {{
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
                font-size: 2.2em;
                page-break-before: always;
            }}
            
            h1:first-child {{
                page-break-before: auto;
            }}
            
            h2 {{
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 8px;
                font-size: 1.8em;
                page-break-before: avoid;
            }}
            
            h3 {{
                color: #34495e;
                font-size: 1.4em;
            }}
            
            h4 {{
                color: #7f8c8d;
                font-size: 1.2em;
            }}
            
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
                font-size: 0.9em;
                page-break-inside: avoid;
            }}
            
            th, td {{
                border: 1px solid #ddd;
                padding: 8px 12px;
                text-align: left;
                vertical-align: top;
            }}
            
            th {{
                background-color: #f8f9fa;
                font-weight: 600;
                color: #2c3e50;
            }}
            
            tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            
            code {{
                background-color: #f1f2f6;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 0.9em;
                color: #e74c3c;
            }}
            
            pre {{
                background-color: #2c3e50;
                color: #ecf0f1;
                padding: 15px;
                border-radius: 5px;
                overflow-x: auto;
                margin: 1em 0;
                page-break-inside: avoid;
            }}
            
            pre code {{
                background: none;
                color: inherit;
                padding: 0;
            }}
            
            blockquote {{
                border-left: 4px solid #3498db;
                margin: 1em 0;
                padding: 0.5em 1em;
                background-color: #f8f9fa;
                font-style: italic;
            }}
            
            ul, ol {{
                margin: 1em 0;
                padding-left: 2em;
            }}
            
            li {{
                margin: 0.3em 0;
            }}
            
            .task-list-item {{
                list-style-type: none;
                margin-left: -1.5em;
            }}
            
            .task-list-item input[type="checkbox"] {{
                margin-right: 0.5em;
            }}
            
            strong {{
                color: #2c3e50;
                font-weight: 600;
            }}
            
            em {{
                color: #7f8c8d;
            }}
            
            a {{
                color: #3498db;
                text-decoration: none;
            }}
            
            a:hover {{
                text-decoration: underline;
            }}
            
            /* 打印样式 */
            @media print {{
                body {{
                    font-size: 12pt;
                    line-height: 1.4;
                    color: black;
                }}
                
                h1 {{
                    font-size: 18pt;
                    color: black;
                }}
                
                h2 {{
                    font-size: 16pt;
                    color: black;
                }}
                
                h3 {{
                    font-size: 14pt;
                    color: black;
                }}
                
                h4 {{
                    font-size: 12pt;
                    color: black;
                }}
                
                table {{
                    font-size: 10pt;
                }}
                
                pre {{
                    font-size: 9pt;
                    background-color: #f5f5f5 !important;
                    color: black !important;
                    border: 1px solid #ccc;
                }}
                
                th {{
                    background-color: #f0f0f0 !important;
                    color: black !important;
                }}
                
                tr:nth-child(even) {{
                    background-color: #f8f8f8 !important;
                }}
                
                a {{
                    color: black !important;
                    text-decoration: underline !important;
                }}
                
                /* 分页控制 */
                h1, h2 {{
                    page-break-after: avoid;
                }}
                
                table, pre, blockquote {{
                    page-break-inside: avoid;
                }}
                
                /* 页面设置 */
                @page {{
                    margin: 2cm;
                    size: A4;
                }}
            }}
            
            /* 页面标题 */
            .document-title {{
                text-align: center;
                color: #2c3e50;
                border-bottom: 3px solid #3498db;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }}
            
            /* 目录样式 */
            .toc {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }}
            
            .toc h2 {{
                margin-top: 0;
                color: #2c3e50;
            }}
        </style>
    </head>
    <body>
        <div class="document-title">
            <h1>{title}</h1>
        </div>
        {html_content}
        
        <script>
            // 自动生成目录
            document.addEventListener('DOMContentLoaded', function() {{
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                if (headings.length > 3) {{
                    const toc = document.createElement('div');
                    toc.className = 'toc';
                    toc.innerHTML = '<h2>目录</h2>';
                    
                    const tocList = document.createElement('ul');
                    headings.forEach((heading, index) => {{
                        const id = 'heading-' + index;
                        heading.id = id;
                        
                        const li = document.createElement('li');
                        const a = document.createElement('a');
                        a.href = '#' + id;
                        a.textContent = heading.textContent;
                        a.style.marginLeft = (parseInt(heading.tagName.charAt(1)) - 1) * 20 + 'px';
                        li.appendChild(a);
                        tocList.appendChild(li);
                    }});
                    
                    toc.appendChild(tocList);
                    document.body.insertBefore(toc, document.body.firstChild.nextSibling);
                }}
            }});
        </script>
    </body>
    </html>
    """
    
    return html_template

def convert_md_to_html(md_file_path, output_dir="html_output"):
    """将单个markdown文件转换为HTML"""
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 读取markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {md_file_path}: {e}")
        return False
    
    # 获取文件名（不含扩展名）
    file_name = Path(md_file_path).stem
    
    # 转换为HTML
    html_content = markdown_to_html(md_content, title=file_name)
    
    # 输出HTML路径
    html_path = Path(output_dir) / f"{file_name}.html"
    
    try:
        # 保存HTML文件
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 转换成功: {md_file_path} -> {html_path}")
        print(f"   💡 请用浏览器打开HTML文件，然后使用Ctrl+P打印为PDF")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败 {md_file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始转换MVP文档为HTML（可打印为PDF）...")
    
    # 需要转换的文件列表
    mvp_files = [
        "docs/MVP技术栈设计方案.md",
        "docs/MVP系统架构详细设计.md", 
        "docs/MVP开发实施计划.md"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for file_path in mvp_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到可转换的文件")
        return
    
    # 转换文件
    success_count = 0
    total_count = len(existing_files)
    
    for file_path in existing_files:
        if convert_md_to_html(file_path):
            success_count += 1
    
    # 输出结果
    print(f"\n📊 转换完成: {success_count}/{total_count} 个文件成功转换")
    
    if success_count > 0:
        print(f"📁 HTML文件保存在: html_output/ 目录下")
        print("\n生成的HTML文件:")
        for file_path in existing_files:
            file_name = Path(file_path).stem
            print(f"  - html_output/{file_name}.html")
        
        print("\n📖 使用说明:")
        print("1. 用浏览器打开HTML文件")
        print("2. 按Ctrl+P打开打印对话框")
        print("3. 选择'另存为PDF'或'打印到PDF'")
        print("4. 调整页面设置（建议A4纸张，边距适中）")
        print("5. 保存PDF文件")

if __name__ == "__main__":
    main()
