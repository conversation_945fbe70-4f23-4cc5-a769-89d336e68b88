#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接Markdown转PDF工具
使用reportlab直接生成PDF，无需浏览器
"""

import os
import sys
from pathlib import Path
import markdown
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape

def setup_fonts():
    """设置中文字体"""
    try:
        # 尝试注册中文字体
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',  # 微软雅黑
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
            r'C:\Windows\Fonts\simhei.ttf',  # 黑体
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('Chinese', font_path))
                    return True
                except:
                    continue
        
        print("⚠️ 未找到中文字体，将使用默认字体")
        return False
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")
        return False

def create_styles(has_chinese_font=False):
    """创建PDF样式"""
    styles = getSampleStyleSheet()
    
    font_name = 'Chinese' if has_chinese_font else 'Helvetica'
    
    # 标题样式
    styles.add(ParagraphStyle(
        name='CustomTitle',
        parent=styles['Title'],
        fontName=font_name,
        fontSize=20,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#2c3e50')
    ))
    
    # 一级标题
    styles.add(ParagraphStyle(
        name='CustomHeading1',
        parent=styles['Heading1'],
        fontName=font_name,
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.HexColor('#2c3e50')
    ))
    
    # 二级标题
    styles.add(ParagraphStyle(
        name='CustomHeading2',
        parent=styles['Heading2'],
        fontName=font_name,
        fontSize=14,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.HexColor('#34495e')
    ))
    
    # 三级标题
    styles.add(ParagraphStyle(
        name='CustomHeading3',
        parent=styles['Heading3'],
        fontName=font_name,
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.HexColor('#7f8c8d')
    ))
    
    # 正文
    styles.add(ParagraphStyle(
        name='CustomNormal',
        parent=styles['Normal'],
        fontName=font_name,
        fontSize=10,
        spaceAfter=6,
        alignment=TA_JUSTIFY,
        leading=14
    ))
    
    # 代码块
    styles.add(ParagraphStyle(
        name='CustomCode',
        parent=styles['Code'],
        fontName='Courier',
        fontSize=9,
        spaceAfter=10,
        spaceBefore=10,
        backColor=colors.HexColor('#f8f9fa'),
        borderColor=colors.HexColor('#e9ecef'),
        borderWidth=1,
        borderPadding=5
    ))
    
    return styles

def parse_markdown_to_elements(md_content, styles):
    """解析Markdown内容为PDF元素"""
    elements = []
    
    # 配置markdown扩展
    extensions = [
        'markdown.extensions.tables',
        'markdown.extensions.fenced_code',
        'markdown.extensions.extra'
    ]
    
    # 转换markdown到html
    md = markdown.Markdown(extensions=extensions)
    html_content = md.convert(md_content)
    
    # 简单的HTML解析
    lines = html_content.split('\n')
    current_text = ""
    in_table = False
    table_rows = []
    
    for line in lines:
        line = line.strip()
        if not line:
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            continue
        
        # 处理标题
        if line.startswith('<h1>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            title = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(unescape(title), styles['CustomHeading1']))
            elements.append(Spacer(1, 0.2*inch))
            
        elif line.startswith('<h2>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            title = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(unescape(title), styles['CustomHeading2']))
            elements.append(Spacer(1, 0.15*inch))
            
        elif line.startswith('<h3>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            title = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(unescape(title), styles['CustomHeading3']))
            elements.append(Spacer(1, 0.1*inch))
            
        elif line.startswith('<h4>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            title = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(f"<b>{unescape(title)}</b>", styles['CustomNormal']))
            
        # 处理表格
        elif line.startswith('<table>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            in_table = True
            table_rows = []
            
        elif line.startswith('</table>'):
            if table_rows:
                # 创建表格
                table = Table(table_rows)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#2c3e50')),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6'))
                ]))
                elements.append(table)
                elements.append(Spacer(1, 0.2*inch))
            in_table = False
            table_rows = []
            
        elif in_table and ('<td>' in line or '<th>' in line):
            # 解析表格行
            cells = re.findall(r'<t[hd]>(.*?)</t[hd]>', line)
            if cells:
                # 清理HTML标签并转义
                clean_cells = []
                for cell in cells:
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    clean_cell = unescape(clean_cell)
                    # 处理换行符
                    clean_cell = clean_cell.replace('<br>', '\n')
                    clean_cells.append(clean_cell)
                table_rows.append(clean_cells)
        
        # 处理代码块
        elif line.startswith('<pre><code>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            code = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(unescape(code), styles['CustomCode']))
            
        # 处理列表项
        elif line.startswith('<li>'):
            if current_text:
                elements.append(Paragraph(current_text, styles['CustomNormal']))
                current_text = ""
            item = re.sub(r'<[^>]+>', '', line)
            elements.append(Paragraph(f"• {unescape(item)}", styles['CustomNormal']))
            
        # 处理段落
        elif line.startswith('<p>'):
            text = re.sub(r'<[^>]+>', '', line)
            current_text += unescape(text) + " "
            
        # 处理其他内容
        else:
            if not line.startswith('<') and line:
                current_text += line + " "
    
    # 添加剩余文本
    if current_text:
        elements.append(Paragraph(current_text, styles['CustomNormal']))
    
    return elements

def convert_md_to_pdf(md_file_path, output_dir="pdfs"):
    """将单个markdown文件转换为PDF"""
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 读取markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {md_file_path}: {e}")
        return False
    
    # 获取文件名（不含扩展名）
    file_name = Path(md_file_path).stem
    
    # 输出PDF路径
    pdf_path = Path(output_dir) / f"{file_name}.pdf"
    
    try:
        # 设置字体
        has_chinese_font = setup_fonts()
        
        # 创建样式
        styles = create_styles(has_chinese_font)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            str(pdf_path),
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # 解析markdown内容
        elements = parse_markdown_to_elements(md_content, styles)
        
        # 添加文档标题
        elements.insert(0, Paragraph(file_name, styles['CustomTitle']))
        elements.insert(1, Spacer(1, 0.3*inch))
        
        # 生成PDF
        doc.build(elements)
        
        print(f"✅ 转换成功: {md_file_path} -> {pdf_path}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败 {md_file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始转换MVP文档为PDF...")
    
    # 需要转换的文件列表
    mvp_files = [
        "docs/MVP技术栈设计方案.md",
        "docs/MVP系统架构详细设计.md", 
        "docs/MVP开发实施计划.md"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for file_path in mvp_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到可转换的文件")
        return
    
    # 转换文件
    success_count = 0
    total_count = len(existing_files)
    
    for file_path in existing_files:
        if convert_md_to_pdf(file_path):
            success_count += 1
    
    # 输出结果
    print(f"\n📊 转换完成: {success_count}/{total_count} 个文件成功转换")
    
    if success_count > 0:
        print(f"📁 PDF文件保存在: pdfs/ 目录下")
        print("\n生成的PDF文件:")
        for file_path in existing_files:
            file_name = Path(file_path).stem
            print(f"  - pdfs/{file_name}.pdf")

if __name__ == "__main__":
    main()
