# DPPaaS 软件技术架构

## 项目信息
- **状态**: 等待中
- **项目**: 数字产品护照
- **任务ID**: TAS-278

## 技术栈概览

### 快速构建MVP原则
如果非常了解另一种方案的同样实现，可以替换。如果只是有猜测，则先用当前技术，之后再优化。

### 核心技术选型

| 技术层面 | 选择技术 | 说明 |
|----------|----------|------|
| **前端App** | iOS, Android, 小程序 | 移动端应用 |
| **前端Web** | Nuxt.js, Tailwind CSS | Vue 3生态系统 |
| **后端** | Go | 微服务架构 |
| **用户系统** | Keycloak | 身份认证与授权 |
| **数据库** | PostgreSQL (Amazon Aurora) | 关系型数据库 |
| **云环境** | AWS | ECS Fargate, Aurora Serverless v2, MSK Serverless, S3 + CloudFront |
| **基础设施** | Terraform + GitHub Actions | 基础设施即代码 |

## 技术架构详解

### 1. 表现层 - Nuxt.js（Vue 3）

| 职责 | 典型实现要点 |
|------|--------------|
| **多租户门户UI** | • 登录/注册、品牌后台、质检机构后台<br>• SFC + Pinia/Composable管理状态<br>• 按租户注入主题、权限 |
| **数字护照视图&编辑器** | • SSR/SSG提供SEO，可生成可分享URL (/passport/{id})<br>• 可视化表单（分步Wizard）上传物料、碳足迹、认证证书(PDF) |
| **实时通知** | • 订阅后端推送的passport_updated WebSocket频道<br>• Toast/Badge提示 |
| **i18n + A11y** | • 内置多语言包（EN/DE/FR/ZH）<br>• 对接欧盟无障碍检查 |
| **边缘渲染** | • Nuxt 3 Nitro输出Cloudflare Workers<br>• 首屏TTFB≈2-5ms<br>• 无需额外Node进程 |

#### 与Next.js的差别
仅限于UI技术栈（React → Vue），API合同、Cookie/OIDC流程保持不变；BFF如仍想写在前端仓，可用Nuxt server/目录或继续让Go统一暴露。

### 2. API/业务层 - Go微服务

| 服务 | 关键功能 | 对接外部 |
|------|----------|----------|
| **passport-service** | • CRUD、版本比对、生成QR/NFC payload<br>• 写Aurora<br>• 发passport_created/updated事件到Kafka | REST/gRPC |
| **registry-service** | • 统一ProductID⇄PassportID解析<br>• 公开查询API<br>• 对外限流&审计 | REST |
| **compliance-service** | • 校验供应链证书<br>• 碳足迹算法（ISO 14067）<br>• 异步校验任务入Kafka | gRPC/Async job |
| **auth-service** | • OIDC provider/租户RBAC<br>• 签JWT，供Nuxt&其他服务验证 | OIDC/gRPC |
| **notification-service** | • 监听Kafka事件<br>• 推邮件、WebSocket、SMS | Kafka/REST |

#### DDD反应堆模式
每个Go服务先事务性写Aurora，再把领域事件写入Kafka → 下游消费。

### 3. 数据层 - Amazon Aurora (PostgreSQL/MySQL)

| 模块 | 存储内容 | 亮点 |
|------|----------|------|
| **Core DB** | passports, product_items, suppliers, audits, users, tenants等 | • ACID、跨AZ高可用<br>• 支持JSONB存半结构化属性 |
| **History DB/分区表** | • 护照历史、变更快照<br>• 按月RANGE + PARTITION<br>• 便于GDPR删除 | 数据治理 |
| **Read Replicas** | • 供报告/BI、全文索引任务<br>• 隔离OLAP读压 | 读写分离 |
| **Logical Replication** | • 可把变更流灌入Kafka（Debezium）<br>• Elasticsearch近实时搜索 | 数据同步 |

### 4. 事件&集成层 - Apache Kafka

| Topic范围 | 发布者 | 订阅者 | 用途 |
|-----------|--------|--------|------|
| **passport_created, passport_updated** | passport-service | notification-service, search-indexer | 更新UI、推送、重建索引 |
| **compliance_check_requested** | Nuxt调用Go API → compliance-service | compliance-service(worker) | 触发异步校验 |
| **compliance_check_result** | compliance-service | passport-service, notification-service | 写回结果、提醒审核人 |
| **cdc_coredb** | Debezium (Aurora) | analytics-pipeline | 数据湖增量拉链、BI报表 |
| **external_webhook_out** | 各Go服务 | partner-gateway | 向外部ERP/PLM广播 |

#### Kafka核心价值
Kafka在解耦（写库即发事件）与可观测（链路追踪、重放）上是DPPaaS核心胶水。你也可用Confluent Cloud Serverless，省管集群运维。

## 数据流示例

### 护照创建流程
```
Nuxt ➜ POST /passports ➜ passport-service（Go）
  ↳ 写Aurora（tx） ↳ passport_created事件 ➜ Kafka
    ↳ notification-service推WebSocket ➜ Nuxt UI刷新
```

### 合规检查流程
```
Nuxt提交 ➜ compliance_check_requested ➜ Kafka
  ↳ compliance-service worker拉任务 ➜ 调ISO14067引擎 ➜ 输出结果
    ↳ compliance_check_result ➜ passport-service更新状态 + Kafka
```

### CDC数据流
```
CDC Stream (cdc_coredb) ➜ partner-gateway过滤规则 ➜ ERP/PLM Webhook
```

## 技术架构总结

| 层 | 主要技术 | 在DPPaaS中的角色 |
|----|----------|-------------------|
| **前端** | Nuxt.js | 多租户门户、护照展示/编辑、SEO SSR、实时通知 |
| **业务** | Go微服务 | 核心领域逻辑、API、鉴权、事件发布 |
| **数据** | Aurora | 关系数据、版本快照、读写分离、CDC |
| **事件** | Kafka | 域事件总线、异步任务、外部集成、可重放 |

### 迁移风险评估
把Next换成Nuxt后，其余Aurora + Kafka + Go设计完全沿用；前端与后端之间仍走REST/gRPC，Kafka事件契约不变，因此风险集中在**UI**层迁移，后端与数据流可以"零感知"过渡。

## 身份认证方案对比

### Keycloak
- **优势**: 完全掌控、EU数据驻留、插件细节多
- **代价**: 多租户、Passkeys已补足，唯一代价是自己打补丁与监控

### Auth0
- **优势**: 快速起盘、SDK丰富、B2C + B2B全覆盖
- **注意**: Organizations、MFA、企业SSO都是阶梯计费，规模化后TCO飙升

### Clerk
- **优势**: 预算敏感、前端团队主导、功能≈Auth0 80%就够
- **计费**: 首10k MAU免费 + $0.02/MAU线性计费，代码集成对前端更友好

## 扩展说明

如果需要更细的部署脚本（Terraform/Helm chart）或事件schema（Avro/Protobuf），可以进一步详细设计。

## 总结

DPPaaS技术架构采用现代微服务架构，通过Nuxt.js提供优秀的用户体验，Go微服务处理核心业务逻辑，Aurora提供可靠的数据存储，Kafka实现事件驱动的松耦合架构。整体设计既保证了系统的可扩展性和可维护性，又满足了欧盟数字产品护照的合规要求。
